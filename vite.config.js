/* eslint-disable no-unused-vars */
import path from "path";
import { defineConfig, splitVendorChunkPlugin } from "vite";
// splitVendorChunk, isCSSRequest

import PluginForViteVue2 from "@vitejs/plugin-vue2";

export default defineConfig(({ mode }) => {
  return {
    resolve: {
      alias: {
        "@": path.resolve("./src"),
        "@css": path.resolve("./src/assets/css"),
      },
    },
    plugins: [PluginForViteVue2(), splitVendorChunkPlugin()],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: "@import '@css/sass.scss';",
        },
      },
    },
    server: {
      proxy: {
        "^/api/": {
          // target: "http://*************:2002/",
          target: "http://www.probim.cn:6291",
          rewrite: (path) => path.replace(/^\/api\//, ""),
          changeOrigin: true, //通过浏览器查看像是"未生效",实际发送给后端的是更改过的Host(与target的host相同)
        },
      },
      port: 3000,
      host: "0.0.0.0",
      cors: true,
      open: true,
    },
    esbuild: {
      treeShaking: true,
      drop: mode === "production" ? ["console", "debugger"] : [],
    },
    build: {
      minify: "esbuild",
      chunkSizeWarningLimit: 500, //kbs
      rollupOptions: {
        output: {
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
          // manualChunks(id) {
          //   if (id.includes("/node_modules/")) {
          //     const name = id.split("/node_modules/")[1].split("/")[0];
          //     for (const key of Object.keys(ChunksMap)) {
          //       if (ChunksMap[key].includes(name)) {
          //         return key;
          //       }
          //     }
          //     return "vendor";
          //   }
          // },

          // eslint-disable-next-line no-unused-vars
          // manualChunks(id, { getModuleInfo, getModuleIds }) {
          //   const getShouldBeVendor = splitVendorChunk();
          //   const chunkName = getShouldBeVendor(id, { getModuleInfo });
          //   if (chunkName === "vendor") {
          //     //1 在node_modules文件夹下
          //     //2 不是css类请求
          //     //3 是被静态引入的
          //     return chunkName;
          //   } else if (id.includes("src")) {
          //     const moduleInfo = getModuleInfo(id);
          //     // console.log(id, "importers", moduleInfo.importers);
          //     // console.log(id, "dynamicImporters", moduleInfo.dynamicImporters);
          //     if (moduleInfo.importers.length + moduleInfo.dynamicImporters.length > 1) {
          //       return "manifest";
          //     }
          //   }
          // },

          // eslint-disable-next-line no-unused-vars
          manualChunks(id) {
            if (id.includes("/node_modules/")) {
              return id.split("/node_modules/")[1].split("/")[0];
            }
          },
        },
      },
    },
  };
});
