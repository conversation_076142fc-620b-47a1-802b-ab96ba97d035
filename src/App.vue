<!-- eslint-disable vue/no-textarea-mustache -->
<!-- eslint-disable vue/html-self-closing -->
<template>
  <reset-pwd v-if="isResetpwdPage" />
  <login v-else-if="isLoginPage" />
  <dashboard v-else-if="isTokenFetched" />
  <login v-else />
</template>

<script>
import { getToken } from "./utils/getToken";
const regForResetpwdPath = /\/resetpwd\b/i;
const regForLoginPath = /\/login\b/i;

export default {
  name: "App",
  components: {
    dashboard: () => import("./Dashboard.vue"),
    login: () => import("./components/Login.vue"),
    "reset-pwd": () => import("./components/ResetPwd.vue"),
  },
  computed: {
    isResetpwdPage() {
      return regForResetpwdPath.test(this.$route.path);
    },
    isLoginPage() {
      return regForLoginPath.test(this.$route.path);
    },
    isTokenFetched() {
      const token = getToken();
      return !!token;
    },
  },
};
</script>
