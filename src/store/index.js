import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

const moduleFilesObj = import.meta.glob("./modules/*.js", { eager: true });
const modules = Object.keys(moduleFilesObj).reduce((acc, moduleKey) => {
  const moduleName = moduleKey.replace(/^\.\/modules\/(.*)\.js$/gi, "$1");
  if (moduleName) {
    acc[moduleName] = moduleFilesObj[moduleKey]?.default ?? {};
    acc[moduleName].namespaced = true;
  }
  return acc;
}, {});

export default new Vuex.Store({ modules });
