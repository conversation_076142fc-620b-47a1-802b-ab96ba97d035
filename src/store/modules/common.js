export default {
  namespaced: true,
  state: () => ({
    userProfile: null,//用户信息
    code: "", //登录时需要的code
    token: "", //登录成功后返回的token
    projectList: [], //项目列表
    currentProject: {}, //当前选中的项目
    fullscreenInfo: {
      isFullscreen: false,
      fullscreenElement: null,
    }, //全屏信息
    isAuthenticating: false,
    isNotificationListVisible: false,
    isLoadingPrjList: false,
    routeViewpointNameMap: {
      "/home": "徐家汇项目平台总览",
      "/people-manage": "徐家汇人员管控",
      "/mold-base": "徐家汇模架装备",
      "/equipment": "设备设施",
      "/env-effect": "徐家汇环境影响",
      "/concrete-pump-performance": "徐家汇泵送分析",
      "/vertical-deformation": "徐家汇竖向变形",
    }, //路由与视点映射
    workerCategoryColorMap: {
      普通工种: "#FF9E14",
      特殊工种: "#F95C59",
      管理人员: "#488CF7",
      机械操作工: "#F7EA17",
      安全干事: "#49D166",
    },
    equipmentsColorMap: {
      围挡: "#488CF7",
      升降机: "#4EDFCB",
      塔吊: "#40ACF7",
    },
    projectTypeColorMap: {
      房建: "#FF614D",
      隧道: "#91CC75",
      水务: "#FAC858",
      道路: "#3BA272",
      桥梁: "#73C0DE",
      改建: "#2EA0F3",
    },
    inoutColorMap: {
      入场: "#E75D4D",
      出场: "#FDC573",
    },
    localMenus: [
      {
        id: "overview",
        label: "平台总览",
        icon: "/images/overview.png",
        to: "/home",
      },
      {
        id: "people-manage",
        label: "人员管控",
        icon: "/images/people-manage.png",
        to: "/people-manage",
      },
      {
        id: "equipment",
        label: "设备设施",
        icon: "/images/equipment.png",
        to: "/equipment",
      },
      {
        id: "mold-base",
        label: "模架装备",
        icon: "/images/mold-base.png",
        to: "/mold-base",
      },
      {
        id: "env",
        label: "环境影响",
        icon: "/images/env.png",
        to: "/env-effect",
      },
    ],
    thirdMenus: [
      {
        id: "underground",
        label: "地下工程",
        icon: "/images/underground.png",
        to: "",
        subMenus: [
          {
            id: "underground-1",
            label: "地下工程施工安全管控系统",
            icon: "/images/underground.png",
            to: "http://***********:5000/project/MonitorDataXJHZX",
          },
          // {
          //   id: "underground-2",
          //   label: "深大基坑钢支撑轴力伺服系统",
          //   icon: "/images/underground.png",
          //   to: "sdjk://",
          //   needInvokeLocalApp: true
          // },
        ],
      },
      {
        id: "surface",
        label: "地面工程",
        icon: "/images/surface.png",
        to: "",
        subMenus: [
          {
            id: "surface-1",
            label: "混凝土泵送性能分析",
            icon: "/images/surface.png",
            to: "/concrete-pump-performance",
            isLocal: true,
          },
          {
            id: "surface-2",
            // label: "混凝土结构实体强度演化系统",
            label: "大体积混凝土温度监测系统",
            icon: "/images/surface.png",
            to: "http://www.jiangong2022.danlu.net/app-build/detection.html",
          },
          {
            id: "surface-3",
            label: "超高结构竖向变形控制系统",
            icon: "/images/surface.png",
            to: "/vertical-deformation",
            isLocal: true,
          },
        ],
      },
      {
        id: "construction",
        label: "施工管理",
        icon: "/images/construction.png",
        to: "",
        subMenus: [
          {
            id: "construction-1",
            label: "绿色建造九宫格管理系统",
            icon: "/images/construction.png",
            // to: "http://121.36.200.205:8080/vms-envmonitor-webapp/",
            to: "",
            needLocalHandle: true
          },
          {
            id: "construction-2",
            label: "施工协同管理系统",
            icon: "/images/construction.png",
            to: "",
            needLocalHandle: true
          },
          {
            id: "construction-3",
            label: "施工方案管理系统",
            icon: "/images/construction.png",
            to: "",
            needLocalHandle: true
          },
        ],
      },
      {
        id: "devops",
        label: "运维管理",
        icon: "/images/devops.png",
        to: "",
        subMenus: [{ id: "devops-1", label: "绿建评价及能耗管控系统", icon: "/images/devops.png", to: "http://222.73.22.141:9228/" }],
      },
    ],
    projectIdsWithoutMoldBase: ["46", "97"], //无模架装备页面的项目
    projectIdsAvailable: ["42", "46", "97"], //可点击的项目
    projectIdPNMap: {
      42: 3,//t2塔楼
      46: 4,//绿建楼
      97: 1//604
    },
    activatedThirdMenu: null, //激活的第三方菜单
    activatedThirdSubmenu: null, //激活的第三方子菜单
    concretePumpPerformanceColorMap: {
      //混凝土泵送性能分析颜色
      "1#": "#E75D4D",
      "2#": "#FDC573",
      "3#": "#6785FB",
      "4#": "#00E17F",
    },
    isSceneLoadComplete: false, //场景是否加载完成
    sensorTypeIdPrefixMap: {
      摄像头: "camera",
      门禁: "doorControl",
      RFID基站: "rfid",
      UWB基站: "uwb",
    },
    notificationCount: 0, //通知数量
    isLoadingNotificationList: false, //加载通知列表标识
    argForGetRiskData: {
      page: 1,
      limit: 10,
      name: "",
      status: -1,
      warningType: "",
      upgradeType: "",
      riskType: "",
    }, //获取报警数据(/beh/environment/GetRiskData)的参数
  }),
  mutations: {
    setCode(state, data) {
      state.code = data;
    },
    setToken(state, data) {
      state.token = data;
    },
    setProjectList(state, data) {
      state.projectList = data;
    },
    setCurrentProject(state, data) {
      state.currentProject = data;
    },
    setFullscreenInfo(state, { isFullscreen = false, fullscreenElement = null }) {
      state.fullscreenInfo = { isFullscreen, fullscreenElement };
    },
    setIsAuthenticating(state, data) {
      state.isAuthenticating = data;
    },
    setIsNotificationListVisible(state, data) {
      state.isNotificationListVisible = data;
    },
    setIsLoadingPrjList(state, data) {
      state.isLoadingPrjList = data;
    },
    setActivatedThirdMenu(state, data) {
      state.activatedThirdMenu = data;
    },
    setActivatedThirdSubmenu(state, data) {
      state.activatedThirdSubmenu = data;
    },
    setIsSceneLoadComplete(state, data) {
      state.isSceneLoadComplete = data;
    },
    setNotificationCount(state, data) {
      state.notificationCount = data;
    },
    setIsLoadingNotificationList(state, data) {
      state.isLoadingNotificationList = data;
    },
    setArgForGetRiskData(state, data) {
      state.argForGetRiskData = data;
    },
    setUserProfile(state, data) {
      state.userProfile = data;
    }
  },
  actions: {
    setCode(context, data) {
      context.commit("setCode", data);
    },
    setToken(context, data) {
      context.commit("setToken", data);
    },
    setProjectList(context, data) {
      context.commit("setProjectList", data);
    },
    setCurrentProject(context, data) {
      context.commit("setCurrentProject", data);
    },
    setFullscreenInfo(context, data = {}) {
      context.commit("setFullscreenInfo", data);
    },
    setIsAuthenticating(context, data) {
      context.commit("setIsAuthenticating", data);
    },
    setIsNotificationListVisible(context, data) {
      context.commit("setIsNotificationListVisible", data);
    },
    setIsLoadingPrjList(context, data) {
      context.commit("setIsLoadingPrjList", data);
    },
    setActivatedThirdMenu(context, data) {
      context.commit("activatedThirdMenu", data);
    },
    setActivatedThirdSubmenu(context, data) {
      context.commit("setActivatedThirdSubmenu", data);
    },
    setIsSceneLoadComplete(context, data) {
      context.commit("setIsSceneLoadComplete", data);
    },
    setNotificationCount(context, data) {
      context.commit("setNotificationCount", data);
    },
    setIsLoadingNotificationList(context, data) {
      context.commit("setIsLoadingNotificationList", data);
    },
    setArgForGetRiskData(context, data) {
      context.commit("setArgForGetRiskData", data);
    },
  },
  getters: {
    userProfile: (state) => state.userProfile,
    code: (state) => state.code,
    token: (state) => state.token,
    projectList: (state) => state.projectList,
    currentProject: (state) => state.currentProject,
    currentProjectId: (state, getters) => getters.currentProject?.id ?? "",
    currentProjectRegionCode: (state, getters) => getters.currentProject?.regionCode ?? "",
    isFullscreen: (state) => state.fullscreenInfo.isFullscreen,
    isAuthenticating: (state) => state.isAuthenticating,
    isNotificationListVisible: (state) => state.isNotificationListVisible,
    isLoadingPrjList: (state) => state.isLoadingPrjList,
    routeViewpointNameMap: (state) => state.routeViewpointNameMap,
    workerCategoryColorMap: (state) => state.workerCategoryColorMap,
    equipmentsColorMap: (state) => state.equipmentsColorMap,
    projectTypeColorMap: (state) => state.projectTypeColorMap,
    inoutColorMap: (state) => state.inoutColorMap,
    activatedThirdMenu: (state) => state.activatedThirdMenu,
    activatedThirdSubmenu: (state) => state.activatedThirdSubmenu,
    localMenus: (state) => state.localMenus,
    thirdMenus: (state) => state.thirdMenus,
    projectIdsWithoutMoldBase: (state) => state.projectIdsWithoutMoldBase,
    concretePumpPerformanceColorMap: (state) => state.concretePumpPerformanceColorMap,
    isSceneLoadComplete: (state) => state.isSceneLoadComplete,
    sensorTypeIdPrefixMap: (state) => state.sensorTypeIdPrefixMap,
    notificationCount: (state) => state.notificationCount,
    isLoadingNotificationList: (state) => state.isLoadingNotificationList,
    argForGetRiskData: (state) => state.argForGetRiskData,
    projectIdsAvailable: (state) => state.projectIdsAvailable,
    projectIdPNMap: (state) => state.projectIdPNMap
  },
};
