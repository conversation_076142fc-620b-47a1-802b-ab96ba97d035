<!-- eslint-disable vue/no-textarea-mustache -->
<!-- eslint-disable vue/html-self-closing -->
<template>
  <main
    v-loading="isAuthenticating || isLoadingPrjList"
    id="appWrapper"
    class="absolute top-[50%] left-[50%] -translate-x-[50%] w-screen h-screen -translate-y-[50%] font-pingfang text-white select-none"
  >
    <!--头部-->
    <app-header class="absolute z-[100] top-0 left-0 right-0" :selected-id="currentProjectId" :option-list="projectList" />

    <!--全屏、切换地图-->
    <div class="absolute z-[50] top-20 left-4 flex space-x-3">
      <fullscreen-button />
      <switch-map ref="refSwitchMap" />
    </div>

    <!--菜单-->
    <collapse direction="left" class="absolute z-[90] top-[200px] left-0">
      <menu-list />
    </collapse>

    <!--滚动警告-->
    <warning class="absolute z-[90] top-[70px] right-0">
      <div class="h-full pl-4 flex items-center justify-end">
        <img class="w-6 h-6" src="./assets/images/!.png" alt="报警" />
        <div class="mx-4">报警提醒</div>
        <div class="flex items-center space-x-4">
          <div>{{ currentRotateWarningItem?.AlarmTime }}</div>
          <div>{{ currentRotateWarningItem?.TypeName }}</div>
          <divide height="16px" :opacity="0.6"></divide>
          <div>{{ currentRotateWarningItem?.Comments }}</div>
        </div>
      </div>
    </warning>

    <!--页面内容-->
    <router-view />

    <!--multiverse渲染容器-->
    <div v-loading="isLoadingScene" element-loading-text="场景加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)" id="renderDom" class="bg-[#1e2336] absolute z-[40] top-0 bottom-0 left-0 right-0 w-full h-full"></div>

    <!--报警弹窗-->
    <dialog-fixed :visible.sync="isDialogVisible" @open="onWarningDialogOpen" @closed="onWarningDialogClose" width="1040px">
      <div class="pt-[30px] font-pingfang text-[14px]">
        <div class="flex items-center justify-center w-full h-[36px]">
          <img src="./assets/images/warning_dialog_title.png" class="w-[575px]" alt="" />
        </div>
        <div class="pt-[24px] px-[16px]">
          <div class="text-right" v-loading="isLoadingNotificationList">
            <div class="bg-[#000A1F] mx-[20px]">
              <div class="flex">
                <el-input
                  clearable
                  v-model="searchInput"
                  class="w-[250px] h-[34px] bg-[#313847] text-[14px]"
                  placeholder="请输入搜索内容"
                  auto-complete="on"
                  @clear="updateNotificationList(1)"
                  @keyup.enter.native="updateNotificationList(1)"
                />
                <div
                  @click="updateNotificationList(1)"
                  class="ml-[10px] w-[94px] h-[34px] bg-[#155BCC] text-[14px] rounded-[3px] flex items-center justify-center cursor-pointer hover:bg-[#568FFA]"
                >
                  搜索
                </div>
              </div>
              <el-table height="324px" class="mt-[18px]" :data="notificationList" :cell-style="cellStyle">
                <el-table-column type="index" width="40" :index="getRowIndex"></el-table-column>
                <el-table-column label="分类" width="80" align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.TypeName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="类型" width="140" align="left">
                  <template slot-scope="scope">
                    <span> {{ scope.row.AlarmType }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="级别" width="104" align="center">
                  <template slot-scope="scope">
                    <div v-if="scope.row.alarmLevel === '超标' || scope.row.alarmLevel === 'Ⅰ级'" class="bg-[#EF424E] px-[14px] rounded-[2px]">
                      {{ scope.row.alarmLevel }}
                    </div>
                    <div v-else class="bg-[#E57029] px-[14px] rounded-[2px]">
                      {{ scope.row.alarmLevel }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="内容" show-overflow-tooltip align="left">
                  <template slot-scope="scope">
                    <span> {{ scope.row.Comments }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="80" align="center">
                  <template slot-scope="scope">
                    <span v-if="scope.row.IsProcess === '已处置'" style="color: #35c954">{{ scope.row.IsProcess }}</span>
                    <span v-else style="color: #ef424e">{{ scope.row.IsProcess }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="时间" width="184" align="left">
                  <template slot-scope="scope">
                    <span>{{ scope.row.AlarmTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="60">
                  <template slot-scope="scope">
                    <el-button type="text" @click="onViewWarningDetailsClick(scope.$index, scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-pagination
              background
              layout="total, prev, pager, next"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              :total="notificationCountLocal"
              @current-change="onCurrentPageChange"
              class="mt-[16px] mr-[25px]"
            />
          </div>
        </div>
      </div>
    </dialog-fixed>

    <!--报警详情-->
    <dialog-fixed :visible.sync="isDialogDetailsVisible" @open="onRiskDetailsDialogOpen" @closed="onRiskDetailsDialogClose" width="1040px">
      <div class="flex items-center justify-center w-full h-[36px] mt-[32px]">
        <img src="./assets/images/warning_dialog_details_title.png" class="w-[575px]" alt="" />
      </div>
      <div @click="isDialogDetailsVisible = false" class="absolute top-[40px] left-[28px] w-[84px] h-[15px] flex items-center cursor-pointer">
        <img src="./assets/images/back-icon.png" alt="" class="w-[16px]" />
        <span class="ml-[8px] text-sm">返回列表</span>
      </div>
      <div class="relative mt-[18px] ml-[20px] mr-[18px]" v-loading="isLoadingDetailsInfo">
        <img class="w-[320px] h-[109px]" src="./assets/images/warning_details_red_bg.png" />
        <img class="w-[60px] h-[50px] absolute left-[22px] top-[14px]" src="./assets/images/pink!.png" />
        <div class="flex flex-row">
          <div class="flex absolute top-[20px] ml-[105px] text-[19px] font-semibold">
            <span>{{ riskDetailsInfo.content }}</span>
            <div
              class="ml-[14px] px-[8px] bg-[#E57029] text-[14px] text-[#FFFFFF] flex items-center justify-center cursor-pointer hover:bg-[#FF7E2E] rounded-sm"
            >
              {{ riskDetailsInfo.level_name }}
            </div>
          </div>
          <div class="flex absolute top-[60px] ml-[105px] font-normal">
            <span class="text-[14px]">风险分类：</span>
            <span class="text-[14px]">{{ riskDetailsInfo.type_name }}</span>
            <span class="text-[14px] ml-[45px]">风险类型：</span>
            <span class="text-[14px]">{{ riskDetailsInfo.type }}</span>
          </div>

          <div class="flex absolute top-[40px] right-[45px] font-normal">
            <span class="text-[14px]">发生时间：</span>
            <span class="text-[14px] ml-[5px]">{{ riskDetailsInfo.risk_time }}</span>
          </div>
        </div>
        <div class="mt-[18px]">
          <img class="w-[325px] h-[27px]" src="./assets/images/warning_detials_bg.png" />
          <div class="flex flex-row relative mt-[35px] ml-[15px]">
            <span class="text-[14px]">处理类型：</span>
            <span
              class="text-[14px] font-semibold ml-[24px]"
              :class="{ 'text-[#35C954]': riskDetailsInfo.process_time, 'text-[#ef424e]': !riskDetailsInfo.process_time }"
            >
              {{ riskDetailsInfo ? (riskDetailsInfo.process_time ? "已处置" : "未处置") : "" }}
            </span>
            <div class="flex absolute right-[140px] font-normal">
              <span class="text-[14px]">处理用户：</span>
              <span class="text-[14px] ml-[24px] font-semibold w-[150px]">{{ riskDetailsInfo.process_user }}</span>
            </div>
          </div>
          <div class="flex flex-row relative mt-[35px] ml-[15px]">
            <span class="text-[14px]">升级类型：</span>
            <span class="text-[14px] ml-[24px]">{{ upgradeTypeNameMap[riskDetailsInfo.upgrade_type] ?? "" }}</span>
            <div class="flex absolute right-[140px] font-normal">
              <span class="text-[14px]">处理时间：</span>
              <span class="text-[14px] ml-[24px] font-semibold w-[150px]">{{ riskDetailsInfo.process_time }}</span>
            </div>
          </div>
        </div>
        <div class="mt-[35px] ml-[14px] flex flex-col">
          <span class="text-[14px]">处理说明：</span>
          <textarea class="mt-[20px] mr-[35px] p-[6px] h-[100px] rounded-[6px] bg-[#1A2336]" v-model="riskDetailsInfo.process_content"></textarea>
        </div>
      </div>
    </dialog-fixed>
  </main>
</template>

<script>
  import Vue from "vue";
  import { mapGetters } from "vuex";
  import Header from "./components/Header.vue";
  import FullscreenButton from "./components/FullScreenButton.vue";
  import SwitchMap from "./components/SwitchMap.vue";
  import MenuList from "./components/MenuList.vue";
  import Warning from "./components/Warning.vue";
  import { sceneJSON } from "./sceneJSON.js";
  import { AppAPI } from "./api/app";
  import { HomeAPI } from "./api/home";
  const regForMoldBase = /\/mold-base\b/i;

  //注册组件
  import VueSeamlessScroll from "vue-seamless-scroll";
  Vue.component("vue-seamless-scroll", VueSeamlessScroll);

  import Collapse from "./components/Collapse.vue";
  Vue.component("collapse", Collapse);

  import Divide from "./components/Divide.vue";
  Vue.component("divide", Divide);

  import DialogFixed from "./components/DialogFixed.vue";
  Vue.component("dialog-fixed", DialogFixed);

  import TimeLine from "./components/TimeLine/TimeLine.vue";
  Vue.component("timeline", TimeLine);

  import dayjs from "dayjs";
  import "dayjs/locale/zh-cn";
  import relativeTime from "dayjs/plugin/relativeTime";
  dayjs.locale("zh-cn");
  dayjs.extend(relativeTime);
  Vue.prototype.$dayjs = dayjs;

  import * as echarts from "echarts";
  Vue.prototype.$echarts = echarts;

  import { nanoid } from "nanoid";
  Vue.prototype.$nanoid = nanoid;

  export default {
    name: "Dashboard",
    components: {
      "app-header": Header,
      "fullscreen-button": FullscreenButton,
      "switch-map": SwitchMap,
      "menu-list": MenuList,
      warning: Warning,
    },
    data() {
      return {
        viewpointDatas: [], //获取到的视点数据列表

        notificationList: [], //弹窗报警列表
        notificationCountLocal: 0,

        isDialogDetailsVisible: false, //详情弹窗是否可见
        isLoadingDetailsInfo: false, //风险详情加载标识
        riskDetailsInfo: {}, //风险详情
        riskDetailsEventId: "", //风险详情RiskEventId
        upgradeTypeNameMap: {
          0: "自动升级",
          1: "手动升级",
        },
        currentRotateWarningIndex: -1, //当前轮转到的警告索引
        warningRotateTimer: null, //警告列表轮转timer
        warningRotateInterval: 1000 * 2, //警告列表轮转周期

        isLoadingScene: false,
      };
    },
    computed: {
      isDialogVisible: {
        get() {
          return this.$store.state.common.isNotificationListVisible;
        },
        set(val) {
          this.$store.commit("common/setIsNotificationListVisible", val);
        },
      },
      ...mapGetters("common", [
        "isAuthenticating",
        "isLoadingPrjList",
        "isSceneLoadComplete",
        "isLoadingNotificationList",
        "argForGetRiskData",
        "projectList",
        "currentProjectId",
        "routeViewpointNameMap",
        "projectIdsWithoutMoldBase",
        "projectIdsAvailable",
      ]),
      searchInput: {
        get() {
          return this.argForGetRiskData.name;
        },
        set(v) {
          this.argForGetRiskData.name = v;
        },
      }, //报警列表搜索内容
      pageSize: {
        get() {
          return this.argForGetRiskData.limit;
        },
        set(v) {
          this.argForGetRiskData.limit = v;
        },
      }, //每页条数
      currentPage: {
        get() {
          return this.argForGetRiskData.page;
        },
        set(v) {
          this.argForGetRiskData.page = v;
        },
      }, //当前页
      currentRotateWarningItem() {
        return this.notificationList?.[this.currentRotateWarningIndex];
      },
    },
    watch: {
      currentProjectId: {
        immediate: false,
        handler(cv, ov) {
          // console.log("watch-currentProjectId", cv);
          if (cv) {
            if (regForMoldBase.test(this.$route.path)) {
              if (this.projectIdsWithoutMoldBase.includes(cv)) {
                this.$router.push("/home");
                return;
              }
            }
            this.onProjectIdChange();
            if (cv === "46") {
              //长三角绿色建筑
              this.changeTo46ProjectView();
            } else if (cv === "42") {
              //徐家汇
              this.changeTo42ProjectView(this.$route);
              // console.log("isSceneLoadComplete", this.isSceneLoadComplete);
              if (this.isSceneLoadComplete) {
                const animationId = "new-crane-animation";
                const newCraneAnimation = window.scene.findAnimation(animationId);
                if (newCraneAnimation) {
                  window.scene.execute("play", { animationIDs: [animationId] });
                } else {
                  this.detectObjectWithAddingAnimation({
                    id: "6c84ed8d-7e10-6538-4deb-e1db9f1f0894^11718",
                    animationName: "new-crane",
                    detectOptions: {
                      found: false,
                      detectionPeriod: 1000,
                      remainingTimes: 200,
                    },
                  });
                }
              }
            } else if (cv === "97") {
              this.changeTo97ProjectView();
            }
          }
          setTimeout(() => {
            window.scene?.render();
          }, 500);
        },
      },
      isSceneLoadComplete: {
        immediate: false,
        handler(cv) {
          // console.log("watch-isSceneLoadComplete", cv);
          if (cv) {
            if (this.currentProjectId === "42") {
              this.changeTo42ProjectView(this.$route);
              if (this.isSceneLoadComplete) {
                const animationId = "new-crane-animation";
                const newCraneAnimation = window.scene.findAnimation(animationId);
                if (newCraneAnimation) {
                  window.scene.execute("play", { animationIDs: [animationId] });
                } else {
                  this.detectObjectWithAddingAnimation({
                    id: "6c84ed8d-7e10-6538-4deb-e1db9f1f0894^11718",
                    animationName: "new-crane",
                    detectOptions: {
                      found: false,
                      detectionPeriod: 1000,
                      remainingTimes: 200,
                    },
                  });
                }
              }
            } else if (this.currentProjectId === "46") {
              //长三角绿色建筑
              this.changeTo46ProjectView();
            } else if (this.currentProjectId === "97") {
              this.changeTo97ProjectView();
            }
            setTimeout(() => {
              window.scene?.render();
            }, 500);
          }
        },
      },
      $route: {
        immediate: false,
        handler() {
          this.changeToProjectView();
        },
      },
    },
    // created() {
    //   document.addEventListener("DOMContentLoaded", this.initMultiverse);
    // },
    mounted() {
      this.initMultiverse();
      this.$nextTick(this.onTokenSettled);
    },
    beforeDestroy() {
      // document.removeEventListener("DOMContentLoaded", this.initMultiverse);
      clearTimeout(this.warningRotateTimer);
      this.resetEngineRelated();
    },
    methods: {
      changeToProjectView() {
        switch (this.currentProjectId) {
          case "42": {
            this.changeTo42ProjectView(this.$route);
            break;
          }
          case "46": {
            this.changeTo46ProjectView();
            break;
          }
          case "97": {
            this.changeTo97ProjectView();
            break;
          }
          default:
            break;
        }
        setTimeout(() => {
          window.scene?.render();
        }, 500);
      },
      //还原97号工程视角
      changeTo97ProjectView() {
        if (this.isSceneLoadComplete) {
          const viewpointName = this.viewpointDatas.find((item) => item.name === "桃浦604项目");
          if (viewpointName) {
            // console.log("changeTo97ProjectView", viewpointName);
            window.scene.restoreViewpoint(viewpointName);
          }
        }
      },
      //还原46号工程视角
      changeTo46ProjectView() {
        if (this.isSceneLoadComplete) {
          const viewpointName = this.viewpointDatas.find((item) => item.name === "绿建楼");
          if (viewpointName) {
            // console.log("changeTo46ProjectView", viewpointName);
            window.scene.restoreViewpoint(viewpointName);
          }
        }
      },
      //注册引擎事件
      bindEngineEvents(_engine) {
        // console.log("注册引擎事件");
        // const events = _engine.events;
        // // // events.mousewheel.on("default", this.onMousewheel);
        // events.singleSelection.on("default", this.onSingleSelection);
      },
      //取消注册引擎事件
      unbindEngineEvents() {
        // console.log("取消注册引擎事件");
        // if (window.scene) {
        //   const events = window.scene.mv.events;
        //   //   // events.mousewheel.off("default", this.onMousewheel);
        //   events.singleSelection.off("default", this.onSingleSelection);
        // }
      },
      //重置引擎相关的东西
      resetEngineRelated() {
        this.unbindEngineEvents();
      },
      //响应选中事件
      onSingleSelection(e) {
        if (window.scene) {
          console.log("点击坐标", e.clientX, e.clientY);
          let geographical = [0, 0, 0];
          let THREE = scene.mv._THREE;
          //传入屏幕坐标，返回三维坐标
          let position = scene.queryPosition(new THREE.Vector2(e.clientX, e.clientY));
          console.log(position);
          // console.log(new THREE.Vector2(e.clientX, e.clientY));
          if (position != "" && position != undefined) {
            //传入三维坐标，返回地理坐标（经纬度）
            geographical = scene.mv.tools.coordinate.vector2mercator(position);
            console.log("坐标位置:" + geographical);
            // 获取完坐标关闭点选方法
            // scene.mv.events.singleSelection.off("default", this.onSingleSelection);

            // const matchedComponent = this.$route.matched?.[0]?.components?.default;
            // if (matchedComponent) {
            //   matchedComponent?.methods?.addDataAnnotation?.(geographical);
            // }
            this.addExampleDataAnnotation(geographical);
          }
        }
      },
      //添加示例锚点
      addExampleDataAnnotation(geographical) {
        if (window.scene) {
          const annotation = scene.addFeature("annotation");
          annotation.origin = [geographical[0], geographical[1]];
          annotation.altitude = geographical[2];
          //增加一个window.page里面包含可以公用的方法
          const anchorJS = `annotation.addEventListener("click",function(e){
              if(e?.target?.id){
                window.scene?.fit2Feature(e.target.id,1,true);
              }
            });`;

          const postData = {
            position: { x: 0, y: 0, z: 0 }, //偏移量
            // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
            content: null,
            // 二维标签html，自定义html字符串
            htmlCode: `
            <div class="flex items-center justify-center w-[34px] h-[34px] bg-[#4D64E1] border-px border-[#FFF] rounded-full pointer-events-none">
              <img class="w-[14px] h-[14px]" src="/anchorIcons/camera.png">
            </div>`,
            // 二维标签样式设置，自定义css字符串
            // 如果二次添加的锚点样式相同，该参数可为空
            cssCode: ``,
            // 二维标签js，自定义字符串
            jsCode: anchorJS,
            // 自定义锚点类型，默认为default,自定义标签为custom
            setType: { anchorType: "custom" },
            visibleDistance: 500,
          };
          // 二维标签数据标识
          annotation.dataKey = "annotation-" + annotation.id;
          // 设置标签数据
          scene.postData(postData, annotation.dataKey);
          // 加载二维标签
          annotation.load();
        }
      },
      //token设定之后的业务逻辑
      async onTokenSettled() {
        const store = this.$store;
        store.commit("common/setIsLoadingPrjList", true);
        const res = await HomeAPI.getProjectList({ withProjectId: false });
        store.commit("common/setIsLoadingPrjList", false);
        if (res?.code === 0) {
          let { prjList } = res.data;
          if (prjList?.length) {
            prjList = prjList.map((item) => {
              return {
                ...item,
                id: item.projectId + "",
                label: item.projectName,
                disabled: !this.projectIdsAvailable.includes(item.projectId + ""),
              };
            });
            store.commit("common/setCurrentProject", prjList[0]);
            this.$nextTick(this.onProjectIdChange);
            store.dispatch("common/setProjectList", prjList);
          }
        }
      },
      //设置警告轮转定时器
      setWarningRotateTimer() {
        clearTimeout(this.warningRotateTimer);
        const _len = this.notificationList?.length;
        if (_len) {
          const _index = this.currentRotateWarningIndex;
          if (_index >= _len - 1) {
            this.currentRotateWarningIndex = 0;
          } else {
            this.currentRotateWarningIndex = _index + 1;
          }
          this.warningRotateTimer = setTimeout(this.setWarningRotateTimer, this.warningRotateInterval);
        }
      },
      //获取行索引
      getRowIndex(index) {
        const { currentPage, pageSize } = this;
        const _t = currentPage - 1;
        return (_t < 0 ? 0 : _t) * pageSize + (index + 1);
      },
      //响应改变项目事件
      onProjectIdChange() {
        this.$store.commit("common/setArgForGetRiskData", { page: 1, limit: 10, name: "", status: -1, warningType: "", upgradeType: "", riskType: "" });
        this.updateNotificationList(1).then((res) => {
          this.$store.commit("common/setNotificationCount", res?.total ?? 0);
        });
      },
      //更新报警信息列表
      async updateNotificationList(currentPage) {
        this.currentPage = currentPage ?? this.currentPage;
        this.$store.commit("common/setIsLoadingNotificationList", true);
        this.notificationList = [];
        this.currentRotateWarningIndex = -1;
        this.setWarningRotateTimer();
        const res = await AppAPI.getRiskData(this.argForGetRiskData);
        this.notificationCountLocal = res?.total ?? 0;
        this.notificationList = res?.code === 0 ? res.data : null;
        this.$store.commit("common/setIsLoadingNotificationList", false);
        this.currentRotateWarningIndex = -1;
        this.setWarningRotateTimer();
        return res;
      },
      //响应报警弹窗打开事件
      //会在多个页面、位置触发
      //查询参数、是否显示由触发页面、位置设置
      //参数由$store.commit("common/setArgForGetRiskData",{page: 1, limit: 10, name: "", status: -1, warningType: "", upgradeType: "", riskType: ""})设置
      //显示由$store.commit("common/setIsNotificationListVisible", true)设置
      onWarningDialogOpen() {
        this.notificationList = [];
        this.updateNotificationList(1).then((res) => {
          this.notificationCountLocal = res?.total ?? 0;
        });
      },
      //响应报警弹窗关闭事件
      onWarningDialogClose() {
        this.$store.commit("common/setArgForGetRiskData", { page: 1, limit: 10, name: "", status: -1, warningType: "", upgradeType: "", riskType: "" });
        this.currentRotateWarningIndex = -1;
        this.notificationCountLocal = 0;
        this.updateNotificationList(1).then((res) => {
          this.$store.commit("common/setNotificationCount", res?.total ?? 0);
        });
        this.$store.commit("common/setIsLoadingNotificationList", false);
      },
      //响应分页事件
      onCurrentPageChange(currentPage) {
        this.updateNotificationList(currentPage);
      },
      //查看详情
      onViewWarningDetailsClick(index, row) {
        this.isDialogDetailsVisible = true;
        this.riskDetailsEventId = row.RiskEventId;
      },
      //响应风险详情open事件
      async onRiskDetailsDialogOpen() {
        this.isLoadingDetailsInfo = true;
        this.riskDetailsInfo = {};
        const res = await AppAPI.getRiskDetails(this.riskDetailsEventId);
        this.isLoadingDetailsInfo = false;
        this.riskDetailsInfo = res?.code === 0 ? res.data : {};
      },
      //响应风险详情close事件
      onRiskDetailsDialogClose() {
        this.riskDetailsEventId = "";
        this.riskDetailsInfo = {};
        this.isLoadingDetailsInfo = false;
      },

      //初始化引擎
      initMultiverse() {
        // 获取承载场景画布dom
        const renderDom = document.getElementById("renderDom");
        if (window.multiverse && renderDom) {
          this.isLoadingScene = true;
          // 实例化引擎
          const multiverse = window.multiverse;
          const engine = new multiverse.mvCore(renderDom);

          // mapbox地图服务的Token,需申请,如不使用Mapbox地图服务,可不设置
          engine.token = window.ProjectConfig.mapboxToken;
          // multiverse.js所在的路径,用于获取静态资源文件
          engine.path = "https://multiverse.vothing.com/";

          // 初始化场景
          try {
            engine.initialize().then((s) => {
              window.scene = s;
              this.bindEngineEvents(engine);
              if (!s.features.has("underlay_wmts")) {
                this.$refs.refSwitchMap.handleAddwmtsFeature("tianditu");
              }
              s.fromJSON(sceneJSON);
              s.stop();
              // s.load();
              this.collectSceneViewPoints();
              this.$store.commit("common/setIsSceneLoadComplete", true);

              setTimeout(() => {
                this.changeToProjectView();
                const animationId = "new-crane-animation";
                const newCraneAnimation = window.scene.findAnimation(animationId);
                if (newCraneAnimation) {
                  window.scene.execute("play", { animationIDs: [animationId] });
                } else {
                  this.detectObjectWithAddingAnimation({
                    id: "6c84ed8d-7e10-6538-4deb-e1db9f1f0894^11718",
                    animationName: "new-crane",
                    detectOptions: {
                      found: false,
                      detectionPeriod: 1000,
                      remainingTimes: 200,
                    },
                  });
                }
              }, 1000);
            });
          } finally {
            this.isLoadingScene = false;
          }
        }
      },

      //周期性探测目标object并设置动画
      detectObjectWithAddingAnimation(arg) {
        const { id, animationName, detectOptions } = arg;
        // console.log("detech", detectOptions);
        if (window.scene && !detectOptions.found && detectOptions.remainingTimes) {
          detectOptions.remainingTimes--;
          // console.log("detech", detectOptions);
          const s = window.scene;
          const target = s.findObject(id);
          if (target) {
            try {
              const animatonService = s.mv.animatonService;
              const numsArray = new Array(30).fill(0);
              const clipName = `${animationName}-clip`;
              let clipExist = false;
              let animationExist = false;
              if (!s.findClip(clipName)) {
                const rotations = animatonService.createRotation(
                  "z",
                  numsArray.map((v, _) => _),
                  numsArray.map((v, _) => -_),
                );
                const clip = animatonService.newClip(clipName, [rotations]);
                s.addClip(clip);
              } else {
                clipExist = true;
              }

              const _animationName = `${animationName}-animation`;
              if (!s.findAnimation(_animationName)) {
                const animation = s.addAnimation(_animationName);
                animation.basePoint = [13502323.272625927, 3654033.6964197545, 256.94511917993293];
                animation.rootObjectID = target.id;
                animation.rootType = "object";
                animation.clip = clipName;
                animation.loop = 2;
                animation.timeScale = 1;
                animation.auto = true;
                animation.active(true);
                detectOptions.found = true;
              } else {
                animationExist = true;
              }

              if (clipExist && animationExist) {
                s.play(_animationName);
              } else {
                if (!detectOptions.found && detectOptions.remainingTimes) {
                  setTimeout(() => {
                    this.detectObjectWithAddingAnimation(arg);
                  }, detectOptions.detectionPeriod);
                }
              }
            } catch {
              // requestAnimationFrame
              setTimeout(() => {
                this.detectObjectWithAddingAnimation(arg);
              }, detectOptions.detectionPeriod);
            }
          } else {
            setTimeout(() => {
              this.detectObjectWithAddingAnimation(arg);
            }, detectOptions.detectionPeriod);
          }
        }
      },
      //收集视点
      collectSceneViewPoints() {
        this.viewpointDatas = [];
        const _viewPoints = window.scene?.viewpoints ?? [];
        if (_viewPoints.length > 0) {
          // 读取视点数据（生成的视点或批注数据都在scene.viewpoints中）
          this.viewpointDatas = _viewPoints.filter((item) => item.type === "viewpoint");
        }
      },
      //切换到视点
      changeTo42ProjectView(route) {
        if (this.isSceneLoadComplete && this.currentProjectId === "42") {
          const { path } = route;
          const viewpointName = this.viewpointDatas.find((item) => item.name === this.routeViewpointNameMap[path]);
          if (viewpointName) {
            // console.log("changeTo42ProjectView", viewpointName);
            window.scene.restoreViewpoint(viewpointName);
          }
        }
      },
      //设置字体颜色
      cellStyle(row, _column, _rowIndex, _columnIndex) {
        // console.log("row", row);
        if (row.column.label === "状态" && row.row.status === "未处置") {
          return "color:#EF424E";
        }
      },
    },
  };
</script>
