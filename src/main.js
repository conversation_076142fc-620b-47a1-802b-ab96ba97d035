import Vue from "vue";

//todo:更改为按需引入
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
Vue.use(ElementUI);

import { http } from "./utils/http.js";
Vue.prototype.$http = http;

import store from "./store/index";
import router from "./router";
import App from "./App.vue";

import "@css/DIN_font.css";
import "@css/tail-wind/index.css";
import "@css/element-ui-override.scss";
import "@css/self-global.scss";

Vue.config.productionTip = false;

new Vue({
  store,
  router,
  render: (h) => {
    return h(App);
  },
}).$mount("#app");
