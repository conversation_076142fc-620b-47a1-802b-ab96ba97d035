import Vue from "vue";
import VueRouter from "vue-router";

import NProgress from "nprogress";
import "nprogress/nprogress.css";

import store from "../store/index";

const thirdMenus = store.getters["common/thirdMenus"];
const localMenus = store.getters["common/localMenus"];

// 注册路由插件
Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/login",
    component: () => import("../components/Login.vue")
  },
  {
    path: "/resetpwd",
    component: () => import("../components/ResetPwd.vue")
  },
  {
    path: "/home",
    component: () => import("../views/Home.vue")
  },
  {
    path: "/people-manage",
    component: () => import("../views/PeopleManage.vue"),
  },
  {
    path: "/equipment",
    component: () => import("../views/Equipment.vue"),
  },
  {
    path: "/env-effect",
    component: () => import("../views/EnvEffect.vue"),
  },
  {
    path: "/mold-base",
    component: () => import("../views/MoldBase.vue"),
  },
  {
    path: "/concrete-pump-performance",
    component: () => import("../views/ConcretePumpPerformance.vue"),
  },
  {
    path: "/vertical-deformation",
    component: () => import("../views/VerticalDeformation.vue"),
  },
];

const router = new VueRouter({
  routes,
  mode: "hash",
});

router.beforeEach((to, from, next) => {
  unsetSelectedThirdMenu();
  NProgress.start();
  next();
});

router.afterEach((to) => {
  if (!localMenus.some((menu) => menu.to === to.path)) {
    setSelectedThirdMenu(to.path);
  }
  NProgress.done();
});

function unsetSelectedThirdMenu() {
  store.commit("common/setActivatedThirdMenu", null);
  store.commit("common/setActivatedThirdSubmenu", null);
}

function setSelectedThirdMenu(path) {
  function findMatchedMenu(items, matcher) {
    if (!Array.isArray(items)) return;
    const stack = JSON.parse(JSON.stringify(items));
    while (stack.length) {
      const current = stack.pop();
      if (matcher(current)) {
        return current;
      } else if (Array.isArray(current?.subMenus)) {
        stack.push(...current.subMenus);
      }
    }
  }
  const matchedThirdSubmenu = findMatchedMenu(thirdMenus, (item) => {
    return item.to === path;
  });
  if (matchedThirdSubmenu) {
    store.commit("common/setActivatedThirdSubmenu", matchedThirdSubmenu);
    const matchedThirdMenu = findMatchedMenu(thirdMenus, (item) => {
      return item.subMenus?.some((submenu) => submenu.to === matchedThirdSubmenu.to);
    });
    matchedThirdMenu && store.commit("common/setActivatedThirdMenu", matchedThirdMenu);
  }
}
export default router;
