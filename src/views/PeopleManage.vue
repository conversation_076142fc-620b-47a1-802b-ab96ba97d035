<template>
  <div class="viewWrapper">
    <!--右侧内容区-->
    <collapse direction="right" class="absolute z-[50] right-0 bottom-2">
      <div class="viewMainBG relative w-[770px] h-[946px] px-[24px] pt-[20px] rounded-tl-[12px] rounded-bl-[12px]">
        <!--人员统计-->
        <div class="peopleToday">
          <!--背景-->
          <img class="w-[376px]" src="../assets/images/people-manage/people-today.png" alt="人员统计" />
          <div class="flex space-x-2 justify-between mt-5 pl-4 h-[224px]">
            <!--在场人数、重点区域-->
            <div class="people flex-1 w-1/2" v-loading="isLoadingTodayTotal">
              <!--在场人数-->
              <div class="flex items-center cursor-pointer" @click="onPersonPresentClick('')">
                <div class="titleBG w-[7rem] h-[2rem] leading-[2rem] pl-2 mr-4"> 在场人数 </div>
                <div class="font-ding text-2xl mr-6">{{ todayTotal?.total }}</div>
                <div class="opacity-90 -mt-1">&gt;</div>
              </div>
              <!--地面建筑钢平台等人数-->
              <div class="flex items-center mt-8 space-x-6">
                <div class="flex items-center space-x-6">
                  <div @click="onPersonPresentClick(2)" class="cursor-pointer">
                    <div class="font-ding text-3xl">{{ todayTotal?.ground }}</div>
                    <div class="text-sm">地面人数</div>
                  </div>
                  <divide width="1px" height="32px" bgc="#fff" :opacity="0.2" />
                </div>

                <div class="flex items-center space-x-6">
                  <div @click="onPersonPresentClick(3)" class="cursor-pointer">
                    <div class="font-ding text-3xl">{{ todayTotal?.building }}</div>
                    <div class="text-sm">建筑区域人数</div>
                  </div>
                  <divide width="1px" height="32px" bgc="#fff" :opacity="0.2" />
                </div>

                <div @click="onPersonPresentClick(4)" class="cursor-pointer">
                  <div class="font-ding text-3xl">{{ todayTotal?.steel_platform }}</div>
                  <div class="text-sm">钢平台区域人数</div>
                </div>
              </div>
              <!--重点区域人数-->
              <div class="importantAreaBG h-16 mt-16">
                <div @click="onPersonPresentClick(5)" class="relative -top-8 cursor-pointer">
                  <div class="font-ding text-3xl">{{ todayTotal?.important_area }}</div>
                  <div class="text-sm">重点区域人数&nbsp;&nbsp;(UWB)</div>
                </div>
              </div>
            </div>
            <!--工种统计-->
            <div class="typeStatus flex-1 w-1/2" v-loading="isLoadingWorkerCategories">
              <div class="titleBG w-[7rem] h-[2rem] leading-[2rem] pl-2 ml-6 mb-4"> 工种统计 </div>
              <div id="workerType" class="w-full h-[176px]"></div>
            </div>
          </div>
        </div>
        <!--人员流量-->
        <div class="peopleFlow mt-8">
          <!--背景-->
          <img class="w-[24rem] h-[2rem]" src="../assets/images/people-manage/flow.png" alt="人员流量" />
          <!--入场出场-->
          <div class="flex mt-6 h-[260px] space-x-2 pl-4">
            <div v-loading="isLoadingInOutChartData" class="flex-1 w-1/2">
              <div class="flex space-x-8">
                <div class="flex items-center">
                  <div class="text-sm mr-3">入场人次</div>
                  <div class="font-ding text-lg">{{ inoutInfo?.entrancetotal }}</div>
                </div>
                <div class="flex items-center">
                  <div class="text-sm mr-3">出场人次</div>
                  <div class="font-ding text-lg">{{ inoutInfo?.exittotal }}</div>
                </div>
              </div>
              <div id="inoutChart" class="h-[14rem]"></div>
            </div>
            <div v-loading="isLoadingPagePersonAlarms" class="flex-1 w-1/2 h-[280px]">
              <vue-seamless-scroll :data="pagePersonAlarmListForView" class="h-full overflow-hidden" :class-option="{ direction: 1, limitMoveNum: 3 }">
                <el-timeline class="mt-4 ml-8 eltimelineCus">
                  <el-timeline-item v-for="v in pagePersonAlarmListForView" :key="v.id">
                    <div class="flex text-[#fff] -ml-[12px] pl-2 pb-[10px]">
                      <div class="flex-none min-w-[120px] -mt-[12px] space-y-2">
                        <div class="flex space-x-2 text-[16px] text-[#FFF1DC]">
                          <div>{{ v.name }}</div>
                          <div>{{ v.work_type }}</div>
                        </div>
                        <div class="flex text-[14px] font-pingfang-regular" :style="getCelsiusStyle(v)">
                          <div class="flex-none mr-2">{{ getCelsiusDesc(v) }}</div>
                          <div class="flex-1">体温{{ v.celsius }}</div>
                        </div>
                      </div>
                      <div class="flex-1 -mt-[12px] space-y-2 text-right">
                        <div class="font-pingfang-regular text-[#B4C6E7]">{{ $dayjs(v.time).fromNow() }}</div>
                        <div class="text-[14px] text-[#2CE7B2]">{{ v.content }}</div>
                      </div>
                    </div>
                    <template #dot>
                      <div class="withGradientBG"></div>
                    </template>
                  </el-timeline-item>
                </el-timeline>
              </vue-seamless-scroll>
            </div>
          </div>
        </div>
        <!--人员风险-->
        <div class="peopleRisk mt-8">
          <!--背景-->
          <img class="w-[24rem] h-[2rem]" src="../assets/images/people-manage/people-risk.png" alt="人员风险" />
          <div class="mt-6">
            <div class="flex">
              <!--风险类型列表-->
              <div class="space-y-4 pl-4 font-pingfang-regular">
                <div
                  v-for="item in riskTypeList"
                  :key="item.id"
                  class="riskTypeItem relative w-28 h-10 flex items-center pl-4 cursor-pointer text-[#D9E8FF] text-opacity-50"
                  :class="{ activated: selectedRiskTypeId === item.id }"
                  @click="onRiskTypeClick(item)"
                >
                  {{ item.label }}
                </div>
              </div>

              <!--健康风险对应内容-->
              <div
                class="flex-1 cursor-pointer"
                key="health"
                v-if="selectedRiskTypeId === 'health'"
                @click="onPersonRiskItemClick('1')"
                v-loading="isLoadingRisk"
              >
                <div class="ml-10">
                  <!--报警、未处置次数-->
                  <div class="flex space-x-2">
                    <div class="riskNumsBG w-56 h-14 rounded-md">
                      <div class="flex h-full justify-between items-center px-6">
                        <div>
                          <span class="font-ding text-4xl mr-6">{{ healthRiskData?.alarm_times }}</span>
                          <span class="text-xs opacity-90">次</span>
                        </div>
                        <div class="text-sm">报警次数</div>
                      </div>
                    </div>
                    <div class="riskNumsBG w-56 h-14 rounded-md">
                      <div class="flex h-full justify-between items-center px-6">
                        <div>
                          <span class="font-ding text-4xl mr-6">{{ healthRiskData?.unhandle_times }}</span>
                          <span class="text-xs opacity-90">次</span>
                        </div>
                        <div class="text-sm">未处理次数</div>
                      </div>
                    </div>
                  </div>
                  <vue-seamless-scroll
                    :data="healthRiskDataForView"
                    :class-option="{
                      direction: 2,
                      limitMoveNum: 3,
                      singleWidth: 284,
                      waitTime: 2000,
                    }"
                    style="overflow-x: hidden; width: 552px"
                  >
                    <div class="flex mt-3 overflow-x-hidden max-w-full">
                      <div v-for="(item, i) in healthRiskDataForView" :key="i" class="riskItem flex-none mr-3 w-[17rem] h-[8rem] rounded-md px-6 py-3">
                        <div class="flex justify-between">
                          <div class="flex items-center -mt-2 space-x-3">
                            <div class="w-3 h-3 rounded-half" :style="getHealthRiskItemStyleObj(item)"></div>
                            <div>{{ $dayjs(item.create_time).format("HH:mm") }}</div>
                          </div>
                          <div class="px-3 py-1 rounded-md" :style="getHealthRiskItemStyleObj(item)"> {{ item.statue }} </div>
                        </div>
                        <div class="space-x-3">
                          <span class="inline-block font-bold">{{ item.name }}</span>
                          <span>{{ item.age }}岁</span>
                        </div>
                        <div class="flex items-center mt-3 space-x-3">
                          <div class="bg-[#d03f30] rounded-md px-2 py-1"> {{ item.text }}:&nbsp;&nbsp;{{ item.value }} </div>
                          <div class="bg-[#272a36] rounded-md px-7 py-1"> {{ item.alarm_level_name }} </div>
                        </div>
                      </div>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>

              <!--位置风险对应内容-->
              <div
                class="flex-1 cursor-pointer"
                key="location"
                v-else-if="selectedRiskTypeId === 'location'"
                @click="onPersonRiskItemClick('2')"
                v-loading="isLoadingRisk"
              >
                <vue-seamless-scroll
                  :data="locationRiskDataForView"
                  :class-option="{
                    direction: 2,
                    limitMoveNum: 2,
                    singleWidth: 230,
                    waitTime: 2000,
                  }"
                  style="overflow-x: hidden; width: 592px"
                >
                  <div class="flex-1 flex items-center mt-[48px]">
                    <div v-for="(item, i) in locationRiskDataForView" :key="i" class="flex items-center locationRisItemWrapper w-[230px]">
                      <div class="relative flex flex-col items-center px-[60px]">
                        <template v-if="item.count > 0">
                          <div class="absolute flex items-center">
                            <span class="text-[18px] text-[#FF6264]"> 风险数 </span>
                            <div class="w-[18px] h-[18px] ml-[7.5px] rounded-half flex justify-center items-center bg-[#FF4A34]"> {{ item.count }} </div>
                          </div>
                          <img class="w-[109px] h-[84px]" src="../assets/images/people-manage/risk-number-bg.png" alt="风险数" />
                          <span class="text-[14px] text-[#FFFFFF]">{{ item.location }}</span>
                          <span class="splitLine absolute top-[50%] right-0 translate-y-[-50%] w-px h-[42px] bg-[#FFF] opacity-20"></span>
                        </template>
                        <template v-else>
                          <span class="absolute text-[18px] text-[#2CE7B2]">安全</span>
                          <img class="w-[109px] h-[84px]" src="../assets/images/people-manage/risk-safe-bg.png" alt="安全" />
                          <span class="text-[14px] text-[#FFFFFF]">{{ item.location }}</span>
                          <span class="splitLine absolute top-[50%] right-0 translate-y-[-50%] w-px h-[42px] bg-[#FFF] opacity-20"></span>
                        </template>
                      </div>
                    </div>
                  </div>
                </vue-seamless-scroll>
              </div>

              <!--行为风险对应内容-->
              <div
                class="flex-1 cursor-pointer"
                key="behavior"
                v-else-if="selectedRiskTypeId === 'behavior'"
                @click="onPersonRiskItemClick('3')"
                v-loading="isLoadingRisk"
              >
                <vue-seamless-scroll
                  :data="behaviorRiskDataForView"
                  :class-option="{
                    direction: 2,
                    limitMoveNum: 2,
                    singleWidth: 230,
                    waitTime: 2000,
                  }"
                  style="overflow-x: hidden; width: 592px"
                >
                  <div class="flex-1 flex items-center mt-[48px]">
                    <div v-for="(item, i) in behaviorRiskDataForView" :key="i" class="flex items-center locationRisItemWrapper w-[230px]">
                      <template v-if="item.count > 0">
                        <div class="relative flex flex-col items-center px-[60px]">
                          <div class="absolute flex items-center cursor-pointer">
                            <span class="text-[18px] text-[#FF6264]"> 风险数 </span>
                            <div class="w-[22px] h-[22px] ml-[8px] rounded-half flex justify-center items-center bg-[#FF4A34]">
                              {{ item.count }}
                            </div>
                          </div>
                          <img class="w-[109px] h-[84px]" src="../assets/images/people-manage/risk-number-bg.png" alt="风险数" />
                          <span class="text-[14px] text-[#FFFFFF]">{{ item.type }}</span>
                          <span class="splitLine absolute top-[50%] right-0 translate-y-[-50%] w-px h-[42px] bg-[#FFF] opacity-20"></span>
                        </div>
                      </template>
                      <template v-else>
                        <div class="relative flex flex-col items-center px-[60px]">
                          <span class="absolute text-[18px] text-[#2CE7B2]">安全</span>
                          <img class="w-[109px] h-[84px]" src="../assets/images/people-manage/risk-safe-bg.png" alt="安全" />
                          <span class="text-[14px] text-[#FFFFFF]">{{ item.type }}</span>
                          <span class="splitLine absolute top-[50%] right-0 translate-y-[-50%] w-px h-[42px] bg-[#FFF] opacity-20"></span>
                        </div>
                      </template>
                    </div>
                  </div>
                </vue-seamless-scroll>
              </div>
            </div>
          </div>
        </div>

        <!--监控视频来源、目标-->
        <div class="absolute z-[50] left-0 top-0" style="transform: translate3D(calc(-100% - 16px),0,0)">
          <div class="flex items-start space-x-2">
            <!--视频来源、目标-->
            <div class="flex h-[276px] text-sm text-left">
              <!--目标-->
              <div v-if="sensorTypeSelected?.children?.length" class="bg-[#313242] p-3 rounded-tl-md rounded-bl-md overflow-auto">
                <div
                  v-for="item in sensorTypeSelected?.children"
                  :key="item.id"
                  class="anchor-target p-3 bg-[#313242] cursor-pointer rounded-md"
                  :class="{ activated: item.id === sensorItemIdSelected }"
                  @click="onSensorItemClick(item, item.id)"
                >
                  <span class="label opacity-60">{{ item.name }}</span>
                </div>
              </div>
              <!--来源-->
              <div v-if="sensorTypeList?.length" class="bg-[#242535] p-3 rounded-tr-md rounded-br-md space-y-2">
                <div
                  v-for="item in sensorTypeList"
                  :key="item.id"
                  class="anchor-source p-3 bg-[#313242] cursor-pointer rounded-md"
                  :class="{ activated: item.id === sensorTypeIdSelected }"
                  @click="onSensorTypeClick(item)"
                >
                  <span class="label opacity-60">{{ item.name }}</span>
                </div>
                <div class="anchor-source p-3 bg-[#313242] cursor-pointer rounded-md" @click="onToggleTrackOfPerson">
                  <span class="label opacity-60">人员轨迹&nbsp;{{ isTrackOfPersonEnabled ? "(开)" : "(关)" }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </collapse>

    <!--弹窗-在场人员列表-->
    <dialog-fixed :visible.sync="isPersonPresentVisible" @close="onPersonPresentListDialogClose">
      <div class="flex items-center justify-center w-full h-[36px] mt-[32px]">
        <img src="../assets/images/people-manage/personnel-present.png" class="w-[575px]" alt="在场人员" />
      </div>
      <div style="height: calc(100% - 72px - 60px)" class="pl-[24px] pr-[8px] pt-[42px] ml-[16px] mr-[16px]" v-loading="isLoadingPersonPresent">
        <div class="grid grid-cols-3 gap-x-[24px] gap-y-[16px] pr-[24px] max-h-full overflow-auto">
          <div
            v-for="(person, i) in personPresentList"
            @click="onPersonDetailClick(person)"
            class="flex py-[16px] px-[12px] rounded-[6px] hover:bg-[hsla(0,0%,100%,0.1)]"
            :key="person.id + i"
          >
            <img :src="person.image" class="object-contain w-[80px] h-[104px]" alt="人员头像" />
            <div class="flex-1 space-y-[8px] pl-[14px] font-pingfang-regular">
              <div class="flex">
                <div class="text-[18px] text-[#FFA42E] font-pingfang-regular">{{ person.name }}</div>
                <div
                  :style="getScoreBg(person.Score)"
                  class="flex items-center ml-[30px] w-[84px] h-[24px] rounded-tr-[11px] rounded-bl-[11px] text-[#fff] text-[12px] font-pingfang"
                >
                  <div class="flex-none px-[6px] h-full flex justify-center items-center bg-[hsla(215,71%,32%,0.2)] rounded-tr-[11px] rounded-bl-[11px]"
                    >安全分</div
                  >
                  <div class="flex-1 h-full flex justify-center items-center">{{ person.Score }}</div>
                </div>
              </div>
              <div class="text-sm text-[#FFF] text-opacity-50">{{ person.age }}岁</div>
              <div class="text-xs text-[#FFF] text-opacity-50">位置: {{ person.address }}</div>
              <div class="text-xs text-[#FFF] text-opacity-50">入场时间: {{ person.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </dialog-fixed>

    <!--弹窗-在场人员详情-->
    <dialog-fixed :visible.sync="presentDetailVisible" @close="onPersonDetailDialogClose">
      <div class="flex items-center justify-center w-full h-[36px] mt-[32px]">
        <img src="../assets/images/people-manage/personnel-present.png" class="w-[575px] h-full" alt="在场人员" />
      </div>
      <div v-if="!isLoadingPersonDetails" class="absolute inline-flex items-center cursor-pointer top-[44px] left-[32px]" @click="backPersonnelList">
        <img src="../assets/images/back-icon.png" alt="返回列表" class="w-[16px] h-[15px]" />
        <span class="ml-[8px] text-[14px]">返回列表</span>
      </div>
      <div style="height: calc(100% - 72px - 56px)" class="mt-[20px] mx-[28px]" v-loading="isLoadingPersonDetails">
        <div class="relative h-[228px]">
          <img src="../assets/images/people-manage/people-detail-bg.png" alt="人员详情背景图" class="absolute w-full h-full" />
          <div class="absolute top-0 bottom-0 left-0 right-0 flex">
            <div class="flex-1 w-[350px] ml-[24px] mt-[48px]">
              <div class="flex">
                <img :src="personInfo?.avatar" alt="人员头像" class="flex-none w-[80px] h-[90px] object-contain" />
                <div class="flex-1 ml-[16px] font-pingfang-regular space-y-3">
                  <div class="flex items-center space-x-[10px]">
                    <div class="text-[16px]">{{ personInfo?.name }}</div>
                    <div class="text-[14px]">{{ personInfo?.gender }}</div>
                    <div class="text-[14px]">{{ personInfo?.extendInfo?.age }}岁</div>
                  </div>
                  <div class="text-[14px] text-[#FFF] text-opacity-50 flex items-center space-x-2">
                    <div>{{ personInfo?.department }}</div
                    ><div>{{ formatIdcard(personInfo?.idcard) }}</div>
                  </div>
                  <div class="text-[14px] text-[#FFF] text-opacity-50">入场时间:&nbsp;&nbsp;{{ personInfo?.extendInfo?.time }}</div>
                </div>
              </div>
              <div class="flex mt-[16px] p-2 bg-[hsla(0,0%,100%,0.1)] rounded-[4px]">
                <div class="relative flex-1 text-center">
                  <i class="absolute top-[50%] right-0 w-[1px] h-[18px] -translate-y-[50%] bg-[hsla(0,0%,100%,0.3)]"></i>
                  <div class="text-[18px]">{{ personInfo?.extendInfo?.address }}</div>
                  <div class="text-sm text-[#FFF] text-opacity-50 font-pingfang-regular">位置</div>
                </div>
                <div class="relative flex-none min-w-[90px] text-center">
                  <i class="absolute top-[50%] right-0 w-[1px] h-[18px] -translate-y-[50%] bg-[hsla(0,0%,100%,0.3)]"></i>
                  <div class="text-[18px] font-ding">{{ personInfo?.heart }}</div>
                  <div class="text-sm text-[#FFF] text-opacity-50 font-pingfang-regular">心率</div>
                </div>
                <div class="relative flex-none min-w-[90px] text-center">
                  <i class="absolute top-[50%] right-0 w-[1px] h-[18px] -translate-y-[50%] bg-[hsla(0,0%,100%,0.3)]"></i>
                  <div class="text-[18px] font-ding">{{ personInfo?.blood_pressure }}</div>
                  <div class="text-sm text-[#FFF] text-opacity-50 font-pingfang-regular">血压</div>
                </div>
                <div class="flex-none min-w-[90px] text-center">
                  <div class="text-[18px] font-ding">{{ personInfo?.temperature }}</div>
                  <div class="text-sm text-[#FFF] text-opacity-50 font-pingfang-regular">体温</div>
                </div>
              </div>
            </div>
            <div id="radar-chart" class="flex-none w-[296px] mx-[16px]"></div>
            <div class="flex-none w-[224px]">
              <div class="text-sm font-pingfang-regular ml-[64px] mt-[8px]">综合评分</div>
              <div class="relative">
                <img src="../assets/images/people-manage/comprehensive-score.png" alt="综合评分背景" class="absolute w-[170px] h-auto top-[16px] left-[16px]" />
                <div class="absolute top-[32px] left-[96px]">
                  <span class="text-[32px] text-[#FFF]">{{ complexScore }}</span>
                  <span class="text-[16px] text-[#FFF] ml-[6px]">%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="height: calc(100% - 228px)" class="flex">
          <div class="w-[522px] h-full pl-[20px]">
            <div class="w-[326] h-[27px]">
              <img src="../assets/images/people-manage/personnel-alarm.png" alt="" class="h-full" />
            </div>
            <div style="height: calc(100% - 27px - 12px)" class="mt-[12px] overflow-auto">
              <template v-if="personAlarmListForView.length < 1">
                <div class="flex justify-center items-center w-full h-full">
                  <div class="w-[50px] h-[82px]">
                    <img src="../assets/images/equipment/no-record.png" class="w-[41px] h-[53px]" alt="" />
                    <div class="text-[hsla(0,0%,100%,0.5)] text-[12px] mt-[14px]">暂无数据</div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div v-for="(item, index) in personAlarmListForView" class="flex items-center h-[55px]" :key="index">
                  <div class="w-[73px] h-[30px]">
                    <div class="text-sm text-[#FFF] text-center">{{ $dayjs(item.time).format("HH:mm:ss") }}</div>
                    <div class="text-[10px] text-[hsla(0,0%,100%,0.4)] text-center">{{ $dayjs(item.time).format("YYYY/MM/DD") }}</div>
                  </div>
                  <div class="relative w-[1px] h-full bg-[#FFF] ml-[24px]">
                    <div class="withGradientMiddleBG"></div>
                  </div>
                  <div class="ml-[29px] w-[264px] text-sm text-[#FFF] font-semibold">{{ item.content }}</div>
                  <div
                    :style="{ 'background-color': item.status == '未处置' ? '#EF424E' : '#5D6A7C' }"
                    style="line-height: 39px"
                    class="ml-[4px] w-[51px] h-[39px] rounded-[6px] text-xs text-[#FFF] text-center font-semibold"
                    >{{ item.status }}</div
                  >
                </div>
              </template>
            </div>
          </div>
          <div class="w-[476px] h-full">
            <div class="w-[326] h-[27px] ml-[15px]">
              <img src="../assets/images/people-manage/penalty-information.png" alt="" class="h-full" />
            </div>
            <div style="height: calc(100% - 27px - 11px)" class="mt-[11px] ml-[28px]">
              <div v-if="personPunishmentList.length < 1" class="flex justify-center items-center w-full h-full">
                <div class="w-[50px] h-[82px]">
                  <img src="../assets/images/equipment/no-record.png" class="w-[41px] h-[53px]" alt="" />
                  <div class="text-[hsla(0,0%,100%,0.5)] text-[12px] mt-[14px]">暂无数据</div>
                </div>
              </div>
              <el-table v-else :data="personPunishmentList" size="medium" height="100%" style="width: 100%">
                <el-table-column
                  v-for="item in personPunishmentTitleList"
                  :key="item.label"
                  :min-width="item.width"
                  :label="item.label"
                  :prop="item.property"
                  show-overflow-tooltip
                />
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </dialog-fixed>

    <!--工人类型,左下角-->
    <!-- <div class="absolute z-[50] bottom-2 left-4">
      <div class="withShadow bg-[hsla(234,36%,17%,0.75)] w-[690px] h-[66px] flex space-x-4 flex-wrap justify-evenly items-center p-2 rounded-[12px]">
        <div v-for="(item, i) in workerCategories" :key="i" class="flex items-center">
          <div class="mr-2 w-4 h-4 rounded-full" :style="{ 'background-color': item.bgc }"></div>
          <div class="text-[14px]">{{ item.name }}</div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { PeopleManageAPI } from "../api/peopleManage";

const CelsiusThreshold = 37.3;

//视频锚点模板
const videoPointTemplate2 = `<div class="absolute top-0 left-full ml-[2px] w-[450px] h-[290px] bg-[#151515] rounded-[6px] p-[12px] cursor-pointer">
                    <div class="flex justify-between">
                      <div class="font-pingfang">
                        <div class="text-[14px]">#videoname#</div>
                      </div>
                      <img class="mt-[6px] cursor-pointer isClose w-[12px] h-[12px]" src="/images/env/close.png" alt="关闭" />
                    </div>
                    <iframe
                      class="mt-[6px] w-full h-[244px] rounded-tl-[6px] rounded-tr-[6px]"
                      src="https://open.ys7.com/ezopen/h5/iframe?url=#videourl#" allowfullscreen frameborder="no">
                    </iframe>
                  </div>`;

export default {
  name: "PeopleManage",
  data() {
    return {
      personPresentList: [], //在场人员列表数据
      isPersonPresentVisible: false,
      isLoadingPersonPresent: false,

      isLoadingPersonDetails: false, //加载人员详情标记

      personInfo: {}, //人员信息
      presentDetailVisible: false,

      personAlarmList: [], //当前人员报警列表
      personPunishmentList: [], //当前人员惩罚列表
      personPunishmentTitleList: [
        { property: "reason", label: "处罚项", width: 145 },
        { property: "score", label: "扣除分", width: 125 },
        { property: "time", label: "时间", width: 165 },
      ],

      complexScore: "", //综合评分

      chartInstances: [], //图表实例数组

      riskTypeList: [
        { id: "health", label: "健康风险" },
        { id: "location", label: "位置风险" },
        { id: "behavior", label: "行为风险" },
      ], //人员风险类型列表
      selectedRiskTypeId: "health", //当前选中的风险类型
      healthRiskData: null,
      locationRiskData: null,
      behaviorRiskData: null,
      isLoadingRisk: false,

      todayTotal: null, //在场人数
      isLoadingTodayTotal: false,

      workerCategoriesFromServer: [], //工种统计
      isLoadingWorkerCategories: false,

      inoutInfo: {}, //人员流量图表数据
      isLoadingInOutChartData: false,

      pagePersonAlarms: [], //人员流量右侧时间线
      isLoadingPagePersonAlarms: false,

      sensorTypeList: null, //传感器类型树列表
      sensorTypeIdSelected: "", //传感器类型Id
      sensorItemIdSelected: "", //传感器类型子项Id
      isTrackOfPersonEnabled: false,
    };
  },
  computed: {
    //根据接口返回的数据和common存储中配置的工种颜色,生成工种数据
    // workerCategories() {
    //   if (this.workerCategoriesFromServer.length) {
    //     return this.workerCategoriesFromServer.map((item) => {
    //       return {
    //         ...item,
    //         bgc: this.workerCategoryColorMap[item.name],
    //       };
    //     });
    //   }
    //   return [];
    // },
    healthRiskDataForView() {
      return this.healthRiskData?.datas ?? [];
    },
    locationRiskDataForView() {
      return this.locationRiskData ?? [];
    },
    behaviorRiskDataForView() {
      return this.behaviorRiskData ?? [];
    },
    pagePersonAlarmListForView() {
      return this.pagePersonAlarms ?? [];
    },
    personAlarmListForView() {
      return this.personAlarmList.slice(0, 3) ?? [];
    },
    sensorTypeSelected() {
      return this.sensorTypeList?.find((item) => item.id === this.sensorTypeIdSelected);
    },
    sensorItemSelected() {
      return this.sensorTypeSelected?.children?.find((item) => item.id === this.sensorItemIdSelected);
    },
    ...mapGetters("common", ["currentProjectId", "workerCategoryColorMap", "inoutColorMap", "sensorTypeIdPrefixMap"]),
    anchorsTypeRegMap() {
      return Object.keys(this.sensorTypeIdPrefixMap).reduce((acc, name) => {
        const prefix = this.sensorTypeIdPrefixMap[name];
        acc[prefix] = new RegExp(`^${prefix}`, "i");
        return acc;
      }, {});
    },
  },
  watch: {
    currentProjectId: {
      immediate: true,
      handler(cv) {
        if (cv) {
          this.updateThePage();
          if (cv !== "42") {
            this.removeAnchors();
            this.isAnchorsAdded = false;
          }
        }
      },
    },
  },
  mounted() {
    this.exposeToWindow();
    this.toggleWorkerAnimation(false);
  },
  beforeDestroy() {
    this.disposeTheChartInstances();
    this.removeAnchors();
    this.deleteExposeToWindow();
    this.toggleWorkerAnimation(false);
  },
  methods: {
    //更新页面
    updateThePage() {
      this.disposeTheChartInstances();
      this.updateSensorTypeList();
      this.updateTodayTotal();
      this.updateWorkerCategoryChart();
      this.$nextTick(this.updateInOutChart);
      this.updatePagePersonAlarms();
      this.updateSelectedRiskTab();
    },
    //释放echart实例
    disposeTheChartInstances() {
      this.chartInstances.forEach((item) => {
        item.dispose();
      });
      this.chartInstances = [];
    },
    //更新基站设备信息
    async updateSensorTypeList() {
      if (this.currentProjectId === "42") {
        const res = await PeopleManageAPI.getSensorTypeTree();
        this.sensorTypeList = res?.code === 0 ? res.data : [];
        // console.log("sensorTypeList",this.sensorTypeList);
        const temp = this.sensorTypeList.find((item) => item.name === "摄像头");
        if (temp) {
          temp.children = temp.children.slice(0, 4);
        }
        const idGenerator = this.$nanoid;
        this.sensorTypeList.forEach((tree) => {
          const idPrefix = this.sensorTypeIdPrefixMap[tree.name];
          const handler = (item) => {
            item.id = `${idPrefix}-${idGenerator()}`;
          };
          this.walkthroughExecHandler(tree, handler);
        });
        this.addAnchors();
      } else {
        this.sensorTypeList = [];
        this.removeAnchors();
      }
    },
    //遍历基站设备树,并执行handler
    walkthroughExecHandler(tree, handler) {
      const stack = [tree];
      let currentItem = null;
      while (stack.length) {
        currentItem = stack.pop();
        const result = handler(currentItem);
        if (result === false) return; //明确返回了false则终止当前树的循环
        if (currentItem.children?.length) {
          stack.push(...currentItem.children);
        }
      }
    },
    //在场人数
    async updateTodayTotal() {
      this.isLoadingTodayTotal = true;
      const res = await PeopleManageAPI.getTodayTotal();
      this.isLoadingTodayTotal = false;
      this.todayTotal = res?.code === 0 ? res.data : null;
    },
    //工种统计
    async updateWorkerCategoryChart() {
      this.isLoadingWorkerCategories = true;
      const res = await PeopleManageAPI.getWorkerCategoryData();
      this.isLoadingWorkerCategories = false;
      const chartDom = document.getElementById("workerType");
      if (chartDom) {
        const myChart = this.$echarts.init(chartDom);
        if (res?.code === 0) {
          this.workerCategoriesFromServer = res.data;
          const dataSource = res.data.reduce((acc, next) => {
            acc[next.name] = next.count;
            return acc;
          }, {});
          const option = {
            tooltip: { trigger: "item" },
            legend: {
              orient: "vertical",
              top: 10,
              right: 0,
              itemGap: 16,
              textStyle: {
                rich: {
                  a: {
                    color: "#fff",
                    fontSize: 14,
                    width: 90,
                  },
                  b: { color: "#fff" },
                },
              },
              formatter: function (name) {
                return `{a|${name}} {b|${dataSource[name]}}`;
              },
            },
            series: [
              {
                type: "pie",
                center: ["30%", "50%"],
                radius: ["45%", "95%"],
                avoidLabelOverlap: false,
                label: {
                  show: false,
                  position: "center",
                },
                emphasis: {
                  label: {
                    show: false,
                    fontSize: "40",
                    fontWeight: "bold",
                  },
                },
                labelLine: {
                  show: false,
                },
                data: Object.keys(dataSource).map((item) => {
                  return {
                    value: dataSource[item],
                    name: item,
                    itemStyle: {
                      color: this.workerCategoryColorMap[item],
                    },
                  };
                }),
              },
            ],
          };
          myChart.setOption(option);
          this.chartInstances.push(myChart);
        } else {
          this.workerCategoriesFromServer = [];
          myChart.dispose();
        }
      }
    },
    //人员流量图表
    async updateInOutChart() {
      this.isLoadingInOutChartData = true;
      let res = await PeopleManageAPI.getTodayPersonExitEntranceData();
      this.isLoadingInOutChartData = false;
      this.inoutInfo = res?.code === 0 ? res.data : null;
      const chartDom = document.getElementById("inoutChart");
      if (chartDom && this.inoutInfo) {
        const myChart = this.$echarts.init(chartDom);
        const inputFlowData = [];
        const outputFlowData = [];
        res = this.inoutInfo.data;
        const xData = Object.keys(res);
        xData.forEach((item) => {
          inputFlowData.push(res[item].entrancetotal);
          outputFlowData.push(res[item].exittotal);
        });
        const markLineData = this.genMarkLineData(xData, inputFlowData, outputFlowData);
        const that = this;
        const option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              label: {
                backgroundColor: "#6a7985",
              },
            },
            formatter(params) {
              return params
                .map((item) => {
                  return `
                    <p>
                      <span style="display:inline-block;border-radius:10px;width:10px;height:10px;
                      background-color:${that.inoutColorMap[item.seriesName]};"></span>
                      <span style="margin-left:4px">${item.name}</span>
                      <span style="margin-left:4px">${item.seriesName}</span>
                      <span style="margin-left:4px">${item.value}</span>
                    </p>`;
                })
                .join("<br/>");
            },
          },
          legend: {
            data: Object.keys(this.inoutColorMap).map((name) => {
              return {
                name,
                itemStyle: {
                  color: this.inoutColorMap[name],
                },
                lineStyle: {
                  color: this.inoutColorMap[name],
                },
              };
            }),
            top: "15%",
            itemGap: 64,
            textStyle: {
              color: "#fff",
              fontSize: 14,
            },
          },
          grid: {
            left: 16,
            right: 16,
            bottom: 24,
          },
          xAxis: [
            {
              type: "category",
              boundaryGap: false,
              data: xData,
              axisTick: { show: false },
              axisLine: { show: false },
              axisLabel: {
                fontSize: 12,
                color: "#B4C6E7",
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              show: false,
            },
          ],
          series: [
            {
              name: "入场",
              type: "line",
              smooth: true,
              lineStyle: {
                width: 3,
                color: this.inoutColorMap["入场"],
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#533b41",
                  },
                  {
                    offset: 1,
                    color: "#191728",
                  },
                ]),
              },
              emphasis: {
                focus: "series",
              },
              data: inputFlowData,
              markLine: {
                symbol: ["none", "none"],
                lineStyle: {
                  color: "hsla(0,0%,100%,0.1)",
                  type: "solid",
                  width: 1,
                },
                data: markLineData,
              },
            },
            {
              name: "出场",
              type: "line",
              smooth: true,
              lineStyle: {
                color: this.inoutColorMap["出场"],
                width: 3,
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(255,187,155,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(255,187,155,0)",
                  },
                ]),
              },
              emphasis: {
                focus: "series",
              },
              data: outputFlowData,
            },
          ],
          dataZoom: [{ type: "inside" }],
        };
        myChart.setOption(option);
        this.chartInstances.push(myChart);
      }
    },
    //生成人员流量入场、出场的标记线数据
    genMarkLineData(xData, inputFlowData, outputFlowData) {
      const genData = [];
      const len = xData.length;
      const maxNum = Math.max(...inputFlowData, ...outputFlowData);
      for (let index = 0; index < len; index++) {
        if (index === 0 || index === len - 1) continue;
        const element = xData[index];
        genData.push([{ coord: [element, 0] }, { coord: [element, maxNum] }]);
      }
      return genData;
    },
    //人员流量右侧的滚动播报
    async updatePagePersonAlarms() {
      this.isLoadingPagePersonAlarms = true;
      const res = await PeopleManageAPI.getPersonAlarms();
      this.isLoadingPagePersonAlarms = false;
      this.pagePersonAlarms = (res?.code === 0 ? res.data : []).map((item, i) => {
        return {
          ...item,
          id: i,
        };
      });
    },
    //响应人员类型风险tab的点击事件
    onRiskTypeClick(item) {
      if (item.id === this.selectedRiskTypeId) return;
      this.selectedRiskTypeId = item.id;
      this.$nextTick(this.updateSelectedRiskTab);
    },
    //更新选中的风险类型
    updateSelectedRiskTab() {
      //prettier-ignore
      switch (this.selectedRiskTypeId) {
          case "health": { this.updateHealthTab(); break; }
          case "location": { this.updateLocationTab(); break; }
          case "behavior": { this.updateBehaviorTab(); break; }
          default: break;
        }
    },

    //健康风险
    async updateHealthTab() {
      this.healthRiskData = [];
      this.isLoadingRisk = true;
      const res = await PeopleManageAPI.getHealthRiskData();
      this.isLoadingRisk = false;
      this.healthRiskData = res?.code === 0 ? res.data : null;
    },
    //位置风险
    async updateLocationTab() {
      this.locationRiskData = [];
      this.isLoadingRisk = true;
      const res = await PeopleManageAPI.getLocationRiskData();
      this.isLoadingRisk = false;
      this.locationRiskData = res?.code === 0 ? res.data : null;
    },
    //行为风险
    async updateBehaviorTab() {
      this.behaviorRiskData = [];
      this.isLoadingRisk = true;
      const res = await PeopleManageAPI.getBehaviorRiskData();
      this.isLoadingRisk = false;
      if (res?.code === 0) {
        const { data } = res;
        if (data?.length) {
          const typeCountMap = data.reduce((acc, next) => {
            const key = next.type;
            if (!acc[key]) {
              acc[key] = {
                type: key,
                count: 1,
              };
            } else {
              acc[key].count += 1;
            }
            return acc;
          }, {});
          this.behaviorRiskData = Object.keys(typeCountMap).map((key) => {
            const item = typeCountMap[key];
            return {
              type: item.type,
              count: item.count,
            };
          });
        }
      }
    },
    //获取健康风险样式
    getHealthRiskItemStyleObj(item) {
      const { statue } = item;
      let bgc = "currentColor";
      if (statue === "已处置") {
        bgc = "#24953c";
      } else if (statue === "未处置") {
        bgc = "#d03f30";
      }
      return { "background-color": bgc };
    },
    //获取人员流量图表右侧时间线体温描述
    getCelsiusDesc(item) {
      const { celsius } = item;
      if (celsius !== null && celsius !== undefined) {
        return Number(celsius) > CelsiusThreshold ? "异常" : "正常";
      } else {
        return "暂时未获取到";
      }
    },
    //获取人员流量图表右侧时间线体温整体样式
    getCelsiusStyle(item) {
      const { celsius } = item;
      if (celsius !== null && celsius !== undefined) {
        return Number(celsius) > CelsiusThreshold ? { color: "#FF6264" } : { color: "#B4C6E7" };
      }
    },

    //响应在场人数点击事件
    // status 检索状态： 2：地面；3：建筑物；4：钢平台。status不传参数时，代表查询全部
    async onPersonPresentClick(status) {
      this.isPersonPresentVisible = true;
      this.updatePersonPresentList(status);
    },
    async updatePersonPresentList(status) {
      this.isLoadingPersonPresent = true;
      this.personPresentList = [];
      const args = { page: 1, size: 1000 };
      if (status) {
        args.status = status;
      }
      const res = await PeopleManageAPI.getPeopleList(args);
      this.isLoadingPersonPresent = false;
      this.personPresentList = (res?.code === 0 ? res.data : []).map((item) => {
        return {
          ...item,
          image: item.avatar,
          address: item.location,
          Score: item.score,
          time: this.$dayjs(item.in_time).format("HH:mm"),
        };
      });
    },
    //响应在场人数列表弹窗关闭事件
    onPersonPresentListDialogClose() {
      this.personPresentList = [];
    },
    //响应点击返回人员列表
    backPersonnelList() {
      this.presentDetailVisible = false;
    },

    //响应点击人员详情
    onPersonDetailClick(people) {
      this.presentDetailVisible = true;
      this.isLoadingPersonDetails = true;
      Promise.all([
        this.updatePersonInfo(people),
        this.updatePersonScore(people),
        this.updatePersonAlarmList(people),
        this.updatePersonPunishmentList(people),
      ]).finally(() => {
        this.isLoadingPersonDetails = false;
      });
    },
    //响应人员详情弹窗关闭
    onPersonDetailDialogClose() {
      this.personInfo = {};
    },
    async updatePersonInfo(people) {
      const res = await PeopleManageAPI.getPersonInfo({ id: people.id, token: this.token, projectId: this.currentProjectId });
      this.personInfo = res?.code === 0 ? res.data : {};
      this.personInfo.extendInfo = people;
    },
    async updatePersonScore(people) {
      if (!people) return;
      const res = await PeopleManageAPI.getPersonScoreData({ id: people.id, token: this.token, projectId: this.currentProjectId });
      this.complexScore = res?.data?.complex ?? "";
      this.$nextTick(() => {
        this.updateRadarChart(res?.code === 0 ? res.data : []);
      });
    },
    updateRadarChart(data) {
      if (!data) return;
      const chartDom = document.getElementById("radar-chart");
      if (chartDom) {
        const myChart = this.$echarts.init(chartDom);
        const option = {
          tooltip: {
            trigger: "item",
          },
          radar: {
            indicator: [
              { name: "职业培训", max: 100 },
              { name: "安全意识", max: 100 },
              { name: "专业能力", max: 100 },
              { name: "心理状态", max: 100 },
              { name: "身体状况", max: 100 },
            ],
            center: ["50%", "55%"],
            splitNumber: 4,
            axisName: {
              formatter: "{value}",
              color: "rgba(255, 255, 255, 0.5)",
            },
            splitArea: {
              areaStyle: {
                color: ["#6E88F9", "#5E7BF8", "#4C6CF7", "#31394A"],
                shadowColor: "rgba(0, 0, 0, 0.2)",
                shadowBlur: 10,
              },
            },
            axisLine: {
              lineStyle: {
                color: "rgba(255, 255, 255, 0.1)",
              },
            },
            splitLine: {
              lineStyle: {
                color: "rgba(255, 255, 255, 0.1)",
              },
            },
          },
          series: [
            {
              type: "radar",
              data: [
                {
                  value: [data.training, data.safe, data.professional, data.mentation, data.health],
                  name: "人员",
                },
              ],
            },
          ],
        };
        myChart.setOption(option);
        this.chartInstances.push(myChart);
      }
    },
    async updatePersonAlarmList(people) {
      const res = await PeopleManageAPI.getPersonAlarmList({ id: people.id, token: this.token, projectId: this.currentProjectId });
      this.personAlarmList = res?.code === 0 ? res.data : [];
    },
    async updatePersonPunishmentList(people) {
      const res = await PeopleManageAPI.getPersonPunishmentList({ id: people.id, token: this.token, projectId: this.currentProjectId });
      this.personPunishmentList = res?.code === 0 ? res.data : [];
    },
    getScoreBg(score) {
      if (score < 60) {
        return { background: "#C42527" };
      } else if (score > 60 && score < 70) {
        return { background: "#FFA42E" };
      } else {
        return { background: "#419B29" };
      }
    },
    formatIdcard(idcard) {
      const str = idcard + "";
      if (idcard && !/^\s*$/g.test(str)) {
        const temp = str.split("");
        return temp.slice(0, 3).join("").padEnd(15, "*") + temp.slice(-4).join("");
      }
    },

    //响应健康风险、位置风险、行为风险点击
    async onPersonRiskItemClick(type) {
      // 参数的含义?
      // {"page":1,"limit":20,"name":"","status":-1,"warningType":"1","upgradeType":"","riskType":"1"} 健康1
      // {"page":1,"limit":20,"name":"","status":-1,"warningType":"1","upgradeType":"","riskType":"2"} 位置2
      // {"page":1,"limit":20,"name":"","status":-1,"warningType":"1","upgradeType":"","riskType":"3"} 行为3
      this.$store.commit("common/setArgForGetRiskData", { page: 1, limit: 10, name: "", status: -1, warningType: "1", upgradeType: "", riskType: type });
      this.$store.commit("common/setIsNotificationListVisible", true);
    },

    //响应传感器类型点击事件
    onSensorTypeClick(item) {
      if (this.sensorTypeIdSelected === item.id) return;
      this.sensorTypeIdSelected = item.id;
      this.sensorItemIdSelected = "";
      this.resetAllUWBPoint();
      this.resetAllVideoPoint();
      this.resetAllDoorControlPoint();
      this.resetAllRFIDPoint();
    },

    //toggle人员轨迹
    onToggleTrackOfPerson() {
      if (this.currentProjectId === "42") {
        this.isTrackOfPersonEnabled = !this.isTrackOfPersonEnabled;
        this.toggleWorkerAnimation(this.isTrackOfPersonEnabled);
      }
    },
    toggleWorkerAnimation(enabled) {
      if (this.currentProjectId === "42") {
        if (window.scene) {
          const s = window.scene;
          let gltf = s.findFeature("7a64c5cb-d1c7-7668-3aaa-6ebe194553c7");
          gltf && (gltf.visible = enabled);

          gltf = s.findFeature("9b30ed3b-212f-2c57-5cf6-7c4a90c27719");
          gltf && (gltf.visible = enabled);

          if (enabled) {
            s.play("Move Worker");
          } else {
            s.stop("Move Worker");
          }
        }
      }
    },

    //响应传感器子项点击事件,需要区分摄像头、门禁、RFID基站、UWB基站:共同行为是聚焦到该元素,重复点击不处理
    onSensorItemClick(detail, id) {
      if (detail || id) {
        const _id = detail?.id || id;
        if (_id === this.sensorItemIdSelected) return;
        this.sensorItemIdSelected = _id;
        this.fit2Feature(_id);
        let isParentFound = false;
        for (const tree of this.sensorTypeList) {
          this.walkthroughExecHandler(tree, (item) => {
            if (item.id === _id) {
              this.sensorTypeIdSelected = tree.id; //选中父项
              isParentFound = true;
              return false; //终止当前树的循环
            }
          });
          if (isParentFound) break;
        }
        const anchorsTypeRegMap = this.anchorsTypeRegMap;
        if (anchorsTypeRegMap.camera.test(_id)) {
          this.onVideoPointClick(_id);
          //提升z-index避免覆盖
          const targetDom = document.getElementById(_id);
          if (targetDom) {
            const currentIndex = Number(targetDom.style.zIndex);
            targetDom.style.zIndex = currentIndex + 1;
          }
        } else if (anchorsTypeRegMap.uwb.test(_id)) {
          this.onUWBPointClick(_id);
        } else if (anchorsTypeRegMap.doorControl.test(_id)) {
          this.onDoorControlPointClick(_id);
        } else if (anchorsTypeRegMap.rfid.test(_id)) {
          this.onRFIDPointClick(_id);
        }
      }
    },

    //响应传感器子项关闭事件,需要区分摄像头、门禁、RFID基站、UWB基站
    onSensorItemClose(anchorType) {
      //prettier-ignore
      switch (anchorType) {
          case "video": { this.resetAllVideoPoint(); break; }
          case "doorControl": { this.resetAllVideoPoint(); break; }
          case "rfid": { this.resetAllVideoPoint(); break; }
          case "uwb": { this.resetAllVideoPoint(); break; }
        }
    },

    //添加摄像头锚点
    addVideoDataAnnotation(geographical, arg) {
      const scene = window.scene;
      if (scene) {
        const annotation = scene.addFeature("annotation", arg.id);
        annotation.origin = [geographical[0], geographical[1]];
        annotation.altitude = geographical[2];
        const postData = {
          position: { x: 0, y: 0, z: 0 }, //偏移量
          // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
          content: null,
          // 二维标签html，自定义html字符串
          htmlCode: `<div data-id="${arg.id}" id="content-${arg.id}" class="videoPointClass relative flex items-center justify-center w-[34px] h-[34px] border border-[#FFF] rounded-full">
                <img data-id="${arg.id}" class="isCameraIcon w-[14px] h-[14px]" src="/anchorIcons/camera.png">
                <div id="videoWrapper-${arg.id}"></div>
                </div>`,
          // 二维标签样式设置，自定义css字符串
          // 如果二次添加的锚点样式相同，该参数可为空
          cssCode: `
            .videoPointClass{background-color: #4D64E1;}
            .videoPointClass.activated{background-color: #FB5101;}`,
          // 二维标签js，自定义字符串
          //增加一个window.page里面包含可以公用的方法
          jsCode: `annotation.addEventListener("click",function(e){
              const target = e?.target??null;
              if(target){
                const targetClassList = target.classList;
                if(targetClassList.contains("videoPointClass")||targetClassList.contains("isCameraIcon")){
                  const id = e.target.dataset.id;
                  window.page?.onSensorItemClick(null,id);
                }else if(targetClassList.contains("isClose")){
                  window.page?.onSensorItemClose("video");
                }
              }
            });`,
          // 自定义锚点类型，默认为default,自定义标签为custom
          setType: { anchorType: "custom" },
          visibleDistance: 500,
        };
        // 二维标签数据标识
        annotation.dataKey = "annotation-" + annotation.id;
        // 设置标签数据
        scene.postData(postData, annotation.dataKey);
        // 加载二维标签
        annotation.load();
      }
    },
    //响应视频锚点点击事件
    onVideoPointClick(id) {
      this.resetAllVideoPoint();
      this.sensorItemIdSelected = id;
      let targetDom = document.getElementById(`content-${id}`);
      if (targetDom) {
        targetDom.classList.add("activated");
        targetDom = document.getElementById(`videoWrapper-${id}`);
        const { name, url } = this.sensorItemSelected;
        targetDom.innerHTML = videoPointTemplate2.replace("#videoname#", name).replace("#videourl#", url);
      }
    },
    //重置所有视频锚点
    resetAllVideoPoint() {
      const targets = document.querySelectorAll(".videoPointClass");
      targets.forEach((item) => {
        this.restoreOriginalZIndex(item);
        item.classList.remove("activated");
        if (item.children[1]) {
          item.children[1].innerHTML = "";
        }
      });
      this.sensorItemIdSelected = "";
    },

    //还原z-index
    restoreOriginalZIndex(item) {
      const target = item.parentNode;
      if (target) {
        const currentIndex = Number(target.style.zIndex);
        if (currentIndex > 9) {
          target.style.zIndex = currentIndex - 1;
        }
      }
    },

    //添加UWB锚点
    addUWBDataAnnotation(geographical, arg) {
      const scene = window.scene;
      if (scene) {
        const annotation = scene.addFeature("annotation", arg.id);
        annotation.origin = [geographical[0], geographical[1]];
        annotation.altitude = geographical[2];

        const postData = {
          position: { x: 0, y: 0, z: 0 }, //偏移量
          // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
          content: null,
          // 二维标签html，自定义html字符串
          htmlCode: `
            <div data-id="${arg.id}">
              <img data-id="${arg.id}" class="w-[50px] m-auto" src="/anchorIcons/uwb.png">
              <div data-id="${arg.id}" id="tooltip-${arg.id}" class="uwbTooltip hidden px-[4px] bg-[rgba(0,0,0,0.4)]">${arg.name}</div>
            </div>
            `,
          // 二维标签样式设置，自定义css字符串
          // 如果二次添加的锚点样式相同，该参数可为空
          cssCode: ``,
          // 二维标签js，自定义字符串
          //增加一个window.page里面包含可以公用的方法
          jsCode: `annotation.addEventListener("click",function(e){
              const id = e.target?.dataset?.id;
              if(id){
                window.page?.onSensorItemClick(null,id);
              }
            });`,
          // 自定义锚点类型，默认为default,自定义标签为custom
          setType: { anchorType: "custom" },
          visibleDistance: 500,
        };
        // 二维标签数据标识
        annotation.dataKey = "annotation-" + annotation.id;
        // 设置标签数据
        scene.postData(postData, annotation.dataKey);
        // 加载二维标签
        annotation.load();
      }
    },
    //响应uwb锚点点击事件
    onUWBPointClick(id) {
      this.resetAllUWBPoint();
      const targetDom = document.getElementById(`tooltip-${id}`);
      targetDom && targetDom.classList.remove("hidden");
    },
    //重置所有uwb锚点
    resetAllUWBPoint() {
      const targets = document.querySelectorAll(".uwbTooltip");
      targets.forEach((item) => {
        item.classList.add("hidden");
      });
    },

    //添加门禁锚点
    addDoorControlDataAnnotation(geographical, arg) {
      const scene = window.scene;
      if (scene) {
        const annotation = scene.addFeature("annotation", arg.id);
        annotation.origin = [geographical[0], geographical[1]];
        annotation.altitude = geographical[2];

        const postData = {
          position: { x: 0, y: 0, z: 0 }, //偏移量
          // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
          content: null,
          // 二维标签html，自定义html字符串
          htmlCode: `
            <div data-id="${arg.id}">
              <img data-id="${arg.id}" class="w-[32px] m-auto" src="/anchorIcons/signal.png">
              <div data-id="${arg.id}" id="tooltip-${arg.id}" class="doorControlTooltip hidden px-[4px] bg-[rgba(0,0,0,0.4)]">${arg.name}</div>
            </div>
            `,
          // 二维标签样式设置，自定义css字符串
          // 如果二次添加的锚点样式相同，该参数可为空
          cssCode: ``,
          // 二维标签js，自定义字符串
          //增加一个window.page里面包含可以公用的方法
          jsCode: `annotation.addEventListener("click",function(e){
              const id = e.target?.dataset?.id;
              if(id){
                window.page?.onSensorItemClick(null,id);
              }
            });`,
          // 自定义锚点类型，默认为default,自定义标签为custom
          setType: { anchorType: "custom" },
          visibleDistance: 500,
        };
        // 二维标签数据标识
        annotation.dataKey = "annotation-" + annotation.id;
        // 设置标签数据
        scene.postData(postData, annotation.dataKey);
        // 加载二维标签
        annotation.load();
      }
    },
    //响应门禁锚点点击事件
    onDoorControlPointClick(id) {
      this.resetAllUWBPoint();
      const targetDom = document.getElementById(`tooltip-${id}`);
      targetDom && targetDom.classList.remove("hidden");
    },
    //重置所有门禁锚点
    resetAllDoorControlPoint() {
      const targets = document.querySelectorAll(".doorControlTooltip");
      targets.forEach((item) => {
        item.classList.add("hidden");
      });
    },

    //添加rfid锚点
    addRFIDDataAnnotation(geographical, arg) {
      const scene = window.scene;
      if (scene) {
        const annotation = scene.addFeature("annotation", arg.id);
        annotation.origin = [geographical[0], geographical[1]];
        annotation.altitude = geographical[2];

        const postData = {
          position: { x: 0, y: 0, z: 0 }, //偏移量
          // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
          content: null,
          // 二维标签html，自定义html字符串
          htmlCode: `
            <div data-id="${arg.id}">
              <img data-id="${arg.id}" class="h-[24px] m-auto" src="/anchorIcons/rfid.png">
              <div data-id="${arg.id}" id="tooltip-${arg.id}" class="rfidTooltip hidden px-[4px] bg-[rgba(0,0,0,0.4)]">${arg.name}</div>
            </div>
            `,
          // 二维标签样式设置，自定义css字符串
          // 如果二次添加的锚点样式相同，该参数可为空
          cssCode: ``,
          // 二维标签js，自定义字符串
          //增加一个window.page里面包含可以公用的方法
          jsCode: `annotation.addEventListener("click",function(e){
              const id = e.target?.dataset?.id;
              if(id){
                window.page?.onSensorItemClick(null,id);
              }
            });`,
          // 自定义锚点类型，默认为default,自定义标签为custom
          setType: { anchorType: "custom" },
          visibleDistance: 500,
        };
        // 二维标签数据标识
        annotation.dataKey = "annotation-" + annotation.id;
        // 设置标签数据
        scene.postData(postData, annotation.dataKey);
        // 加载二维标签
        annotation.load();
      }
    },
    //响应rfid锚点点击事件
    onRFIDPointClick(id) {
      this.resetAllRFIDPoint();
      const targetDom = document.getElementById(`tooltip-${id}`);
      targetDom && targetDom.classList.remove("hidden");
      const controller = scene.mv.controller;
      console.log("azimuthAngle", controller.azimuthAngle);
      console.log("polarAngle", controller.polarAngle);
    },
    //重置所有rfid锚点
    resetAllRFIDPoint() {
      const targets = document.querySelectorAll(".rfidTooltip");
      targets.forEach((item) => {
        item.classList.add("hidden");
      });
    },

    //暴露接口到全局环境
    exposeToWindow() {
      window.page = this;
    },
    //移除暴露的接口
    deleteExposeToWindow() {
      window.page = null;
    },

    //加入锚点
    addAnchors() {
      if (this.isAnchorsAdded || !this.sensorTypeList?.length) return;
      if (window.scene) {
        console.log("加入锚点");
        this.isAnchorsAdded = true;

        // const cameras = this.sensorTypeList[0]?.children ?? [];
        const cameras = this.sensorTypeList.find((tree) => tree.name === "摄像头")?.children ?? [];
        if (cameras.length) {
          cameras[0] && this.addVideoDataAnnotation([121.42916189598549, 31.195045901767827, 198.94291044380645], cameras[0]);
          cameras[1] && this.addVideoDataAnnotation([121.42906797253312, 31.19527154364944, 198.94243604508156], cameras[1]);
          cameras[2] && this.addVideoDataAnnotation([121.42938096406937, 31.19535804227347, 198.9422541860168], cameras[2]);
          cameras[3] && this.addVideoDataAnnotation([121.42949314921077, 31.195086078897788, 198.9428259739902], cameras[3]);
        }

        const doorControls = this.sensorTypeList.find((tree) => tree.name === "门禁")?.children ?? [];
        if (doorControls.length) {
          doorControls[0] && this.addDoorControlDataAnnotation([121.42916831525042, 31.197275410056434, 2.799967302234477], doorControls[0]);
          doorControls[1] && this.addDoorControlDataAnnotation([121.43097023576394, 31.195379959161286, 2.8530503816340023], doorControls[1]);
        }

        const rfids = this.sensorTypeList.find((tree) => tree.name === "RFID基站")?.children ?? [];
        //scene.findFeature("rfid-forTest").parent.fitter.fit2FeatureCenter((new scene.mv._THREE.Vector3).applyMatrix4(scene.findFeature("rfid-forTest").bpt), 20)
        if (rfids.length) {
          rfids[0] && this.addRFIDDataAnnotation([121.42901061257817, 31.19507852221652, 76.25045965132483], rfids[0]);
          rfids[1] && this.addRFIDDataAnnotation([121.42919940923201, 31.194917198039875, 108.75084097480243], rfids[1]);
          rfids[2] && this.addRFIDDataAnnotation([121.42935159369534, 31.19493609273289, 98.7707407190568], rfids[2]);
          rfids[3] && this.addRFIDDataAnnotation([121.42898490658325, 31.195025882395427, 61.77040718333318], rfids[3]);
          rfids[4] && this.addRFIDDataAnnotation([121.42904218728046, 31.194944824260645, 52.750392520357494], rfids[4]);
          rfids[5] && this.addRFIDDataAnnotation([121.4290549272011, 31.19493769282066, 39.2502950229576], rfids[5]);
        }

        // const uwbs = this.sensorTypeList[3]?.children ?? [];
        const uwbs = this.sensorTypeList.find((tree) => tree.name === "UWB基站")?.children ?? [];
        if (uwbs.length) {
          uwbs[3] && this.addUWBDataAnnotation([121.42906227376028, 31.19530734920683, 199.94236796482213], uwbs[3]);
          uwbs[1] && this.addUWBDataAnnotation([121.42912671246012, 31.195026018608374, 199.94296241906918], uwbs[1]);
          uwbs[2] && this.addUWBDataAnnotation([121.42949493380951, 31.195093658063556, 199.9428194966932], uwbs[2]);
          uwbs[0] && this.addUWBDataAnnotation([121.42942789485994, 31.19537016038663, 199.9422352435398], uwbs[0]);
          uwbs[4] && this.addUWBDataAnnotation([121.43056280940249, 31.196107829930313, 0], uwbs[4]);
          uwbs[5] && this.addUWBDataAnnotation([121.42844165635995, 31.195246575995398, 0], uwbs[5]);
          uwbs[6] && this.addUWBDataAnnotation([121.42893995281298, 31.19641350303219, 0], uwbs[6]);
          uwbs[7] && this.addUWBDataAnnotation([121.4305694152963, 31.195479517959924, 0], uwbs[7]);
        }
      }
    },

    //移除锚点
    removeAnchors() {
      console.log("移除锚点");
      if (window.scene) {
        const reg = /^\s*(?:camera-|uwb-|doorControl-|rfid-)/i;
        const scene = window.scene;
        window.scene.features.forEach((feature) => {
          if (reg.test(feature?.id + "")) {
            // console.log("remove", feature.id);
            scene.removeFeature(feature.id, true);
          }
        });
      }
    },
    //将目标元素聚焦到画布中央
    fit2Feature(id) {
      const scene = window.scene;
      if (scene) {
        const target = scene.findFeature(id);
        target && scene.fit2Feature(target, 1, true);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.rightSide {
  right: 0;
}
.importantAreaBG {
  background: url("../assets/images/people-manage/wave.png") no-repeat center / contain border-box;
}
.titleBG {
  background: url("/images/title-bg.png") no-repeat left center / cover border-box;
}
.riskNumsBG {
  background: url("../assets/images/people-manage/riskNumsBG.png") no-repeat center / cover border-box;
}
.viewMainBG {
  background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9), hsla(236, 33%, 18%, 0.9));
}
.withShadow {
  box-shadow: -1px 0px 25px 0px rgba(14, 15, 15, 0.1);
}
.riskTypeItem {
  &::after {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
    content: "";
    background-color: #6586ff;
    transform: scale3d(1, 0, 1);
    transition: transform 0.3s;
  }
  &.activated,
  &:hover {
    color: #d9e8ff;
    @apply text-opacity-100;
    background: linear-gradient(90deg, #1e5fd5 0%, rgba(46, 91, 246, 0) 100%);
  }
  &.activated::after {
    transform: scale3d(1, 1, 1);
  }
}

.riskItem {
  background-color: hsla(218, 20%, 16%, 0.8);
}
.anchor-source,
.anchor-target {
  &:hover .label {
    opacity: 1;
  }
}
.anchor-target {
  &.activated {
    background-color: #464755;
    .label {
      opacity: 1;
    }
  }
}
.anchor-source {
  &.activated {
    background-color: #e57029;
    .label {
      opacity: 1;
    }
  }
}
.withGradientBG {
  $size: 1.5rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-7px, -50%, 0);
  width: $size;
  height: $size;
  border-radius: 50%;
  background: radial-gradient(circle calc($size / 3) at 50% 50%, #fff, #fff 50%, hsla(240, 5%, 40%, 0.2) calc(50% + 1px), hsla(240, 5%, 40%, 0.2) 100%)
    no-repeat center/cover border-box;
}
.withGradientMiddleBG {
  $size: 1.5rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-12px, -50%, 0);
  width: $size;
  height: $size;
  border-radius: 50%;
  background: radial-gradient(circle calc($size / 3) at 50% 50%, #fff, #fff 50%, hsla(240, 5%, 40%, 0.2) calc(50% + 1px), hsla(240, 5%, 40%, 0.2) 100%)
    no-repeat center/cover border-box;
}
::v-deep .el-table {
  background-color: #000a1f;
  &::before {
    background-color: unset;
  }
  .el-table__header {
    thead {
      color: #fff;
      tr {
        border-bottom: unset;
        th.el-table__cell.is-leaf {
          border-bottom: unset;
        }
        th {
          font-weight: 600;
        }
        .el-table__cell {
          padding: 6px 0;
          background-color: #5e616e;
        }
      }
    }
  }
  .el-table__body {
    tbody {
      tr {
        &:hover > td {
          background-color: rgba(255, 255, 255, 0.1);
        }
        background-color: #000a1f;
        color: #fff;
        font-weight: 600;
        .el-table__cell {
          padding: 10px 0;
          border-bottom: unset;
        }
      }
    }
  }
}
::v-deep .el-timeline.eltimelineCus {
  .el-timeline-item {
    padding-bottom: 38px;
    .el-timeline-item__tail {
      position: absolute;
      left: 4.5px;
      height: 100%;
      border-left: 1px solid hsla(0, 0%, 100%, 0.2);
    }
  }
  .el-timeline-item:last-of-type {
    padding-bottom: 0;
  }
}
</style>
