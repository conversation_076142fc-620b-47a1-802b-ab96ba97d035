<template>
  <div
    :style="{
      height: height,
    }"
    class="scale-wrapper"
  >
    <div class="com-scale">
      <div
        v-for="(item, index) in length + 1"
        :key="item"
        :style="{
          top: index * (100 / length) + '%',
        }"
        class="tick"
      >
        <div class="value"> {{ (length - index) * step - min }}mm </div>
      </div>
      <div class="name">
        {{ yAxisName }}
      </div>
    </div>
    <div :style="style" class="color-bar">
      <div v-if="showValue" :class="className" class="value-text">
        <!--value:m-->
        {{ valueText || value }}
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: "",

    components: {},
    props: {
      min: {
        type: Number,
        default: 0,
      },
      showValue: {
        type: Boolean,
        default: true,
      },
      // bottom:{
      //     type:[Number,String],
      //     default: 'initial'
      // },
      // top:{
      //     type:String,
      //     default: 'initial'
      // },
      value: {
        type: [Number, String],
        default: 0,
      },
      valueText: {
        type: [Number, String],
        default: 0,
      },
      length: {
        type: Number,
        default: 10,
      },
      height: {
        type: String,
        default: "100%",
      },
      percentage: {
        type: [Number, String],
        default: 0,
      },
      yAxisName: {
        type: String,
        default: "",
      },
      step: {
        type: Number,
        default: 100,
      },
    },
    data() {
      return {
        style: {},
        className: "",
      };
    },

    computed: {},
    watch: {
      percentage: {
        handler() {
          const v = this.value;
          console.log("this.value", this.value);
          let style = {};
          if (v >= 0) {
            style = {
              top: "initial",
              bottom: "50%",
              height: this.percentage * 100 + "%",
            };
            this.className = "top";
          }
          if (v < 0) {
            style = {
              top: "50%",
              bottom: "initial",
              height: this.percentage * 100 + "%",
            };
            this.className = "bottom";
          }
          this.style = style;
          if (this.yAxisName === "钢平台位移") {
            console.log("钢平台位移");
            style.top = "initial";
            style.bottom = "0";
            console.log("??", v, this.percentage, this.style);
          }
          // console.log('style',v,style)
        },
        immediate: true,
      },
    },

    created() {},

    methods: {},
  };
</script>

<style lang="scss" scoped>
  .scale-wrapper {
    display: inline-flex;
    position: relative;
    padding-right: count(10);
    //height: count(620);
  }

  .com-scale {
    border-right: 1px solid rgba(255, 255, 255, 0.5);
    position: relative;
    white-space: nowrap;
    width: count(80);
  }

  .name {
    position: absolute;
    top: count(-36);

    right: -1em;
    font-size: count(12);

    font-weight: 500;
    color: #cae0ff;

    @media (max-width: 1400px) {
      & {
        //height: count(40);
        top: 15%;
        margin-left: -1em;
        writing-mode: tb-rl;
      }
    }
  }

  .tick {
    position: absolute;
    width: count(10);
    height: 1px;
    background: rgba(255, 255, 255, 0.5);
    top: 0%;
    right: -1px;

    .value {
      font-size: count(10);

      font-weight: 600;
      color: rgba(195, 203, 255, 0.5);

      position: relative;

      display: inline-block;

      margin-left: count(-11);
      transform: translate(-100%, -75%);
    }
  }
  .color-bar {
    position: absolute;
    bottom: 0;
    //top: initial;
    width: count(5);
    border-radius: 10em;
    right: 0;

    background: rgb(69, 195, 160);
    transition: height 0.5s linear;
  }
  .value-text {
    position: absolute;
    color: #f5f2f0;
    transform: translate(0.5em, 0);
    white-space: nowrap;
    //bottom: 0;
    //top: 30%;
    //&.top{
    //  top: 0;
    //}
    //&.bottom{
    //  bottom: 0;
    //}
  }
</style>
