<template>
  <div class="scale-wraper">
    <div class="base-info">
      <div>
        <span class="label">钢平台当前高度：</span>
        {{ mold_base_height }} m
      </div>
      <div> <span class="label"> 本次爬升起始高度： </span>{{ mold_last_height }} m </div>
    </div>

    <div
      :style="{
        bottom: percentage1 * 100 + '%',
      }"
      class="img-panel-wrapper"
    >
      <img class="panel" src="../../../assets/legMonitorImages/yougang.png" />
      <div class="cenng-box">
        <div
          :style="{
            height: percentage2 * 100 + '%',
          }"
          class="zhu"
        >
          <div class="value-text">
            {{ value }}
            <!--{{ value>0?Number(value)+500:500+2250+Number(value) }}-->
          </div>
          <img alt="" class="ceng" src="../../../assets/legMonitorImages/ceng1.jpg" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: "",

    components: {},
    props: {
      value: {
        type: [Number, String],
        default: 0,
      },
      percentage1: {
        type: [Number, String],
        default: 1,
      },
      percentage2: {
        type: [Number, String],
        default: 1,
      },
      // eslint-disable-next-line vue/prop-name-casing
      mold_base_height: {
        type: [Number, String],
        default: 0,
      },
      // eslint-disable-next-line vue/prop-name-casing
      mold_last_height: {
        type: [Number, String],
        default: 0,
      },
    },

    data() {
      return {};
    },

    computed: {},

    watch: {},

    created() {
      // this.$nextTick(()=>{
      // setInterval(()=>{
      //     this.percentage1 = this.percentage1 - 0.1
      //     this.percentage2 = this.percentage2 - 0.1
      // },300)
      // })
    },

    methods: {},
  };
</script>

<style lang="scss" scoped>
  .scale-wraper {
    display: inline-flex;
    flex-direction: column;
    height: 100%;
    width: 100px;
    overflow: hidden;
    flex: 1;
    position: relative;
  }
  .test {
    transition: all 0.5s linear;
  }
  .img-panel-wrapper {
    display: flex;
    position: absolute;
    //width: count(134);
    //height: count(40);
    width: count(300);
    margin-left: count(30);
    //width: count(340);
    //height: count(408);
    transition: all 0.5s linear;
    //679x816
    .panel {
      width: 100%;
      height: 100%;
    }
    .ceng {
      width: 100%;

      left: 0;
      bottom: count(25);
    }
    .cenng-box {
      position: absolute;
      height: count(139);
      top: count(144);
      left: 0;
      right: 0;
      width: 100%;
      //background: #000;

      .zhu {
        position: relative;
        height: 10%;
        background-image: url("../../../assets/legMonitorImages/zhu2.jpg");
        background-repeat-x: no-repeat;
        background-size: 100%;
        transition: all 0.5s linear;

        &::after,
        &::before {
          content: "";
          top: 0;
          //bottom: -13.02083vw;
          width: 130%;
          left: -15%;
          border-top: 1px dashed rgba(195, 203, 255, 0.5);
          position: absolute;
        }
        &::before {
          top: initial;
          bottom: 0;
        }
      }
      .ceng {
        position: absolute;
        bottom: 0;
        transform: translateY(100%);
      }
    }
  }
  .value-text {
    position: absolute;
    color: rgba(195, 203, 255, 1);
    right: count(-35);
    bottom: 0;
    //transform: translate(100%,45%);
    //top: 50%;
    //color: #000;
  }
  .base-info {
    position: absolute;
    left: count(30);
    color: rgba(195, 203, 255, 1);
    line-height: 1.6;
    .label {
      width: 9em;
      display: inline-block;
      white-space: nowrap;
      //text-align: right;
      //background: #000;
    }
  }
</style>
