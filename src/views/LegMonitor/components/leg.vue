<template>
  <div
    :class="{
      'scale-x': scaleX,
    }"
    class="leg-wrapper"
  >
    <!--{{ Math.min(1,percentage*400/350 ) }}-->
    <div class="img-wrapper">
      <img alt="" class="img-1" src="../../../assets/legMonitorImages/img-11.png" />
      <img
        :style="{
          width: 0.8005 + 1.3 * Math.min(1, percentage) + 'cm',
        }"
        class="img-2"
        src="../../../assets/legMonitorImages/img-12.png"
      />
      <img
        :style="{
          width: 3 + 'cm',
        }"
        class="img-2"
        src="../../../assets/legMonitorImages/133.jpg"
      />
      <img alt="" class="img-3" src="../../../assets/legMonitorImages/img-13.png" style="" />
      <div class="img-4-box">
        <div class="rule-box">
          <scale2
            :length="2"
            :percentage="percentage"
            :percentage-text="percentageText"
            :step="175"
            style="height: 20px; position: absolute; right: 0"
            y-axis-name="牛腿行程"
          />
        </div>

        <img alt="" class="img-4" src="../../../assets/legMonitorImages/img-14.png" style="" />
      </div>
    </div>
  </div>
</template>
<script>
  import scale2 from "./scale2.vue";

  export default {
    name: "",

    components: {
      scale2,
    },
    props: {
      value: {
        type: [Number, String],
        default: 0,
      },
      percentage: {
        type: Number,
        default: 1,
      },
      percentageText: {
        type: [Number, String],
        default: 0,
      },
      percentage1: {
        type: Number,
        default: 1,
      },
      scaleX: {
        type: Boolean,
        default: false,
      },
    },

    data() {
      return {};
    },

    computed: {},

    watch: {},

    created() {},

    methods: {},
  };
</script>

<style lang="scss" scoped>
  .leg-wrapper {
    position: relative;
    display: flex;
    justify-content: flex-end;
    //justify-content: center;
    //background: #fff;
    //width: 83%;
    //background: #fff;
    height: count(264);
    //transform: scaleX(-1,1);
    &.scale-x {
      transform: scaleX(-1);

      .scale-wrapper {
        ::v-deep .name {
          transform: scaleX(-1) translate(100%, -50%);
        }

        ::v-deep .value-name {
          transform: scaleX(-1) translate(-120%, -50%);
        }

        ::v-deep .value {
          transform: scaleX(-1) translate(50%, -175%);
        }
      }
    }
    //
    //.img-tui1 {
    //
    //  // /2
    //  height: 100%;
    //  position: relative;
    //  z-index: 10;
    //
    //}
    //
    //.right {
    //
    //  position: absolute;
    //  left: count(165);
    //  height: 100%;
    //
    //  .por-content {
    //    position: relative;
    //    width: 4cm;
    //    left: 35px;
    //    height: 100%;
    //
    //  }
    //
    //  .tui2-box {
    //    position: absolute;
    //    //width: 120px;
    //    height: 100%;
    //
    //    transition: all 0.5s linear;
    //
    //  }
    //}
  }
  .img-wrapper {
    width: count(355);
    width: count(376);
    //overflow: hidden;
    position: relative;
    display: flex;
    //background: #000;
  }
  .img-1 {
    position: relative;
    right: -2.5px;
    height: 100%;
  }
  .img-2 {
    height: 100%;
    //width:0.5cm;
    //width: 3.2cm !important;
    position: relative;
    z-index: 10;
    transition: all 0.5s linear;
  }
  .img-3 {
    position: relative;
    z-index: 10;
    height: 100%;
    //padding-right: 120px;
    //background: #38f;
  }
  .img-4-box {
    position: absolute;
    right: 0;
    height: 100%;
    .img-4 {
      height: 100%;
      //display: none;
    }
    .rule-box {
      position: absolute;
      top: -30px;
      right: count(28);
    }
    &:before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      background-image: url("../../../assets/legMonitorImages/img-14.png");
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center center;
      opacity: 0.5;
      z-index: 100;
      //background: #000;
    }
  }
</style>
