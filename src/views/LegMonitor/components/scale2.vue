<template>
  <div
    :style="{
      height: height,
    }"
    class="scale-wrapper"
  >
    <div class="com-scale">
      <div
        v-for="(item, index) in length + 1"
        :key="item"
        :style="{
          left: index * (100 / length) + '%',
        }"
        class="tick"
      >
        <div class="value"> {{ index * step }}mm </div>
      </div>
      <div class="name">
        {{ yAxisName }}
      </div>
    </div>
    <div
      :style="{
        width: percentage * 100 + '%',
      }"
      class="color-bar"
    >
      <span class="value-name">{{ percentageText }}mm</span>
    </div>
  </div>
</template>
<script>
  export default {
    name: "",

    components: {},
    props: {
      length: {
        type: Number,
        default: 10,
      },
      height: {
        type: String,
        default: "100%",
      },
      percentage: {
        type: [Number, String],
        default: 0,
      },
      percentageText: {
        type: [Number, String],
        default: 0,
      },
      yAxisName: {
        type: String,
        default: "",
      },
      step: {
        type: Number,
        default: 100,
      },
    },

    data() {
      return {};
    },

    computed: {},

    watch: {},

    created() {},

    methods: {},
  };
</script>

<style lang="scss" scoped>
  .scale-wrapper {
    //display: inline-flex;
    position: relative;
    padding-bottom: count(20);
    //height: count(620);
  }

  .com-scale {
    border-bottom: 1px solid rgba(255, 255, 255, 0.5);
    position: relative;

    white-space: nowrap;
    width: 2.5cm;
    width: 4cm;
    width: 1.3cm;
    z-index: 10;
  }

  .name {
    position: absolute;
    //top: count(-36);
    transform: translate(-100%, -50%);
    left: -20px;
    font-size: count(16);

    font-weight: 500;
    color: #cae0ff;

    @media (max-width: 1400px) {
      & {
        //height: count(40);
        top: 15%;
        margin-left: -1em;
        writing-mode: tb-rl;
      }
    }
  }

  .tick {
    position: absolute;
    height: count(10);
    width: 1px;
    background: rgba(255, 255, 255, 0.5);
    bottom: 0% !important;
    right: -1px;
    z-index: 10;
    .value {
      font-size: count(10);

      font-weight: 600;
      color: rgba(195, 203, 255, 0.5);

      position: relative;

      display: inline-block;

      //margin-left: count(-11);
      transform: translate(-50%, -175%) rotate(0deg) scale(0.7);
      //background: #000;
      display: none;
    }
    &:nth-child(1),
    &:nth-child(3) {
      .value {
        display: inline-block;
      }
    }
  }

  .color-bar {
    position: absolute;
    bottom: 0;
    height: count(5);
    width: 4cm;
    border-radius: 10em;
    left: 0;
    background: rgb(69, 195, 160);
    transition: all 0.5s linear;
    &:before,
    &:after {
      content: "";
      //width: 1px;
      //height: 100px;
      top: count(-70);
      bottom: count(-250);
      border-left: 1px dashed rgba(195, 203, 255, 0.5);
      //background: #f42;
      position: absolute;
      left: 0;
      //z-index: -1;
    }
    &:after {
      left: initial;
      right: 0;
    }
  }
  .value-name {
    position: absolute;
    right: 0;
    color: rgb(69, 195, 160);
    height: 1em;
    transform: translate(120%, -50%);
  }
</style>
