<template>
  <div class="platform">
    <div class="content-wrap">
      <div class="relative z-[2] overflow-hidden ml-[30px] flex items-center">
        <div class="chunk-left-box row-chunk">
          <img alt="" class="bg-img" src="../../assets/legMonitorImages/bg-wang.png" />
          <div class="content-view mt-[100px]">
            <!--{{ gpt }}-->
            <scale :length="9" :percentage="value3Pro" :step="2000" :value="value3" :value-text="value3Text" y-axis-name="钢平台位移" />
            <scale
              :length="2"
              :min="2750"
              :percentage="value1Pro"
              :step="2750"
              :value="value1"
              :value-text="value1Text"
              show-value
              style="height: 30%; z-index: 10"
              y-axis-name="油缸伸出量"
            />
            <machine
              :mold_base_height="mold_base_height"
              :mold_last_height="mold_last_height"
              :percentage1="value3Pro"
              :percentage2="value1Pro"
              :value="value1 + 'mm'"
            />
            <!--     :percentage2="1-dataObj['T1-W-1'].percentage2" -->
          </div>
        </div>
        <div class="row-chunk pt-[10px] ml-[25px]">
          <div class="chunk-left-box">
            <div class="card-head">
              <el-button type="primary"> T1-3-1# </el-button>
            </div>
            <div class="card-body">
              <leg :percentage="Number(dataObj['T1-3-1'].percentage)" :percentage-text="Number(dataObj['T1-3-1'].percentageText)" scale-x />
            </div>
          </div>

          <div class="chunk-left-box">
            <div class="card-head">
              <el-button type="primary"> T1-2-1# </el-button>
            </div>
            <div class="card-body">
              <leg :percentage="Number(dataObj['T1-2-1'].percentage)" :percentage-text="Number(dataObj['T1-2-1'].percentageText)" scale-x />
            </div>
          </div>
        </div>
        <div class="row-chunk pt-[10px] ml-[25px]">
          <div class="chunk-left-box">
            <div class="card-head">
              <el-button type="primary"> T1-3-2# </el-button>
            </div>
            <div class="card-body">
              <leg :percentage="Number(dataObj['T1-3-2'].percentage)" :percentage-text="Number(dataObj['T1-3-2'].percentageText)" />
            </div>
          </div>

          <div class="chunk-left-box">
            <div class="card-head">
              <el-button type="primary"> T1-2-2# </el-button>
            </div>
            <div class="card-body">
              <leg :percentage="Number(dataObj['T1-2-2'].percentage)" :percentage-text="Number(dataObj['T1-2-2'].percentageText)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { MoldBaseAPI } from "../../api/moldBase";
  import scale from "./components/scale.vue";
  import machine from "./components/machine.vue";
  import leg from "./components/leg.vue";

  export default {
    // 塔吊筒监控
    name: "Platform",
    components: {
      scale,
      machine,
      leg,
    },
    data() {
      return {
        // 油缸位移
        value1: "",
        value1Pro: "",
        value1Text: "",
        // 钢平台
        value3: "",
        value3Pro: "",
        value3Text: "",
        //     钢平台当前高度：mold_base_height 单位m
        mold_base_height: "",
        mold_last_height: "",
        // 本次爬升高度：mold_last_height 单位m

        // 本次爬升高度
        realtime: "",
        jc_data_rate: "",
        gpt_data_rate: "",
        timer: null,
        // 钢平台位移
        gpt: 0,
        gptValue: "",
        dataObj: {
          // 油缸位移
          "T1-W-1": {
            percentage: 0,
          },
          "T1-3-2": {
            percentage: 0,
          },
          "T1-3-1": { percentage: 0 },
          "T1-2-2": { percentage: 0 },
          "T1-2-1": { percentage: 0 },
        },
      };
    },
    // computed: {},
    // mounted() {
    //   if (!this.isLoadingLegMonitorData) {
    //     this.doBusiness();
    //   }
    // },
    // 销毁定时器
    beforeDestroy() {
      this.dispose();
    },
    methods: {
      doBusiness() {
        this.gpt_data_rate = (this.user?.gpt_data_rate || 60) * 1000;
        this.start();
        this.timer = setInterval(() => {
          this.start();
        }, this.gpt_data_rate);
      },
      dispose() {
        if (this.timer) {
          clearInterval(this.timer);
          this.timer = null;
        }
        this.reset();
      },
      reset() {
        this.value1 = "";
        this.value1Pro = "";
        this.value1Text = "";
        this.value3 = "";
        this.value3Pro = "";
        this.value3Text = "";
        this.mold_base_height = "";
        this.mold_last_height = "";
        this.realtime = "";
        this.jc_data_rate = "";
        this.gpt_data_rate = "";
        this.timer = null;
        this.gpt = 0;
        this.gptValue = "";
        this.dataObj = {
          "T1-W-1": { percentage: 0 },
          "T1-3-2": { percentage: 0 },
          "T1-3-1": { percentage: 0 },
          "T1-2-2": { percentage: 0 },
          "T1-2-1": { percentage: 0 },
        };
      },
      start() {
        Promise.all([this.getMoldBaseInfo(), this.getMonitorDataByPoints()]);
        // 计算油缸位移
        MoldBaseAPI.getMonitorDataByPoints(null, { params: { points: '"T1-W-1", "T1-W-1-SUM"' } }).then((res) => {
          let value1 = 0;
          res.data.forEach((item) => {
            item.monitor_value = Number(Number(item.monitor_value).toFixed(1));
            value1 = value1 + item.monitor_value;
          });
          console.log("T1-W-1", value1, res);
          this.value1 = value1.toFixed(1);
          this.value1Pro = Math.abs(value1) / 5500;
          this.value1Text = `${this.value1}mm[塔吊筒位移0101：${res.data[0]?.monitor_value ?? ""}mm]`;
        });
      },
      getMoldBaseInfo() {
        MoldBaseAPI.getMoldBaseInfo().then((res) => {
          // 钢平台当前高度：mold_base_height 单位m
          // 本次爬升高度：mold_last_height 单位m
          // res.data.mold_base_height= 4
          // res.data.mold_last_height = 3
          const height = res.data.mold_base_height - res.data.mold_last_height || 0;
          console.log("height", height);
          // height = Number(height).toFixed(1)
          this.value3 = height;
          this.value3Pro = height / 18;
          this.value3Text = (height * 1000).toFixed(1) + "mm";
          this.mold_base_height = res.data.mold_base_height;
          this.mold_last_height = res.data.mold_last_height;
          // 最低时为 306 最高为4500 单位m
          // const moldBaseHeight = 225.05
          // const offsetHeight = Math.min(18,res.data.mold_base_height - moldBaseHeight)
          // const gpt = offsetHeight/18
          // this.gpt =  Math.min(1, gpt )
          // this.gptValue = res.data.mold_base_height
        });
      },
      getMonitorDataByPoints() {
        MoldBaseAPI.getMonitorDataByPoints(null, { params: { points: '"T1-2-1", "T1-2-2", "T1-3-1", "T1-3-2", "T1-W-1"' } }).then((res) => {
          this.realtime = res.data[0]?.realtime ?? "";
          const dataObj = {
            // 油缸位移
            "T1-W-1": {},
            "T1-3-2": {},
            "T1-3-1": {},
            "T1-2-2": {},
            "T1-2-1": {},
          };
          res.data.forEach((item) => {
            item.monitor_value = Number(item.monitor_value).toFixed(1);
            if (item.monitor_point_code !== "T1-W-1") {
              item.percentage = Math.min(350, item.monitor_value) / 350;
              item.percentageText = Math.min(350, item.monitor_value);
              console.log("item.monitor_value", item.monitor_value);
              // console.log('item.percentageText',item.percentageText)
            }
            if (item.monitor_point_code === "T1-W-1") {
              // item.monitor_value =0
              item.percentage = Math.abs(item.monitor_value) / 4500;

              // if(item.monitor_value>=0){
              let value = item.monitor_value;
              value = value > 0 ? Number(value) + 500 : 500 + 2250 + Number(value);
              item.percentage2 = 1 - Math.abs(value) / 2750;
              // }

              // item.percentage2 = 0.5-item.percentage||Math.abs(item.monitor_value*1000)/4500
            }
            dataObj[item.monitor_point_code] = item;
          });
          // dataObj['T1-W-1'].percentage = (Number(dataObj['T1-W-1'].monitor_value)+2250)/5000
          this.dataObj = dataObj;
        });
      },
    },
  };
</script>

<style lang="scss" rel="stylesheet/scss">
  .platform {
    text-align: initial;

    .content-wrap {
      position: relative;

      margin: 0 count(34) 0;
      height: count(915);

      box-sizing: border-box;
      //overflow: auto;
      &::after {
        content: "";
        background: #000;
        filter: blur(220px);
        display: block;
        position: absolute;
        z-index: 0;
        height: 150%;
        top: -30%;
        left: 0;
        right: 0;
        width: 500px !important;
        margin: 0 auto;
      }

      .poa-msg-wrapper {
        position: absolute;
        top: count(-50);
        height: count(50);
        display: flex;
        right: 0;
        //display: none;
        //background: #fff;
        img {
          width: count(23);
          margin-right: count(16);
        }

        .err,
        .normal {
          color: #fe635d;
          height: 100%;
          display: flex;
          align-items: center;
        }

        .normal {
          margin-left: count(40);
          color: rgb(67, 196, 195);
        }
      }
    }

    .row-chunk {
      height: count(800);
      overflow: hidden;
    }

    .chunk-left-box {
      position: relative;
      width: count(576);
      //   border-radius: count(36);
      //   background-color: #151931;
      overflow: hidden;
    }

    .card-head {
      height: count(70);

      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: count(15);
      padding-left: count(30);
    }

    .img-title {
      height: count(21);
      display: flex;
      margin-top: count(50);
      margin-bottom: count(50);

      img {
        height: 100%;
        margin: 0 auto;
      }
    }

    .card-body {
      height: count(320);
      //padding-top: count(10);
      //background: #000;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: count(60);
    }

    .bg-img {
      position: absolute;
      width: 94%;
      height: 68%;
      top: count(200);
      left: 3%;
    }

    .content-view {
      position: relative;
      display: flex;
      align-items: flex-end;
      flex-wrap: nowrap;
      height: 80%;
      padding-left: count(20);
    }
  }
</style>
