<template>
  <div class="homeViewWrapper">
    <collapse direction="right" class="absolute z-50 bottom-2 right-0">
      <div class="tabsWrapper w-[388px] h-[958px] px-[24px] rounded-tl-[34px] rounded-bl-[34px]">
        <div class="flex items-center pt-[20px]">
          <div
            v-for="tab in tabs"
            :key="tab.id"
            class="tab-item w-[128px] px-6 py-2 text-[18px] cursor-pointer"
            :class="{ activated: tab.id === tabIdSelected }"
            @click="onTabClick(tab)"
          >
            {{ tab.label }}
          </div>
        </div>
        <template v-if="tabIdSelected === 'groupOverview'">
          <div class="tabContentWrapper" key="groupOverview">
            <!--总开工数-->
            <div class="mt-[16px]">
              <div class="flex justify-between items-center">
                <img src="../assets/images/home/<USER>" alt="总开工数" />
                <div class="font-ding text-[22px] -ml-[32px] h-[34px]">
                  {{ isLoadingTotalGoing ? "" : totalGoingData?.count ?? "" }}
                </div>
              </div>
              <!--数据更新和echart图表-->
              <div class="mt-3" v-loading="isLoadingTotalGoing">
                <div class="font-pingfang-light text-right text-[12px] h-[18px]">
                  {{ isLoadingTotalGoing ? "" : totalGoingData?.updateTime ? `更新于${$dayjs(totalGoingData.updateTime).format("YYYY/MM/DD HH:mm:ss")}` : "" }}
                </div>
                <div id="totalGoing" class="w-full h-[8rem]"></div>
              </div>
            </div>

            <!--工程类型汇总-->
            <div class="mt-[20px]">
              <span class="titleBg pl-2 py-1">工程类型汇总</span>
              <div id="projectTypeSummary" class="w-full h-[11rem] mt-3" v-loading="isLoadingProjectTypeSummary"></div>
            </div>

            <!--行业分类统计-->
            <div class="mt-[20px]">
              <span class="titleBg pl-2 py-1">行业分类统计</span>
              <div class="mt-4" v-loading="isLoadingTradeCategoriesStatistics">
                <div class="tradeCategoriesList h-[160px]">
                  <div class="flex h-[40px] items-center py-2 font-bold bg-[RGBA(94,97,110,1)] pr-[6px]">
                    <div class="tableTitle flex-1 text-center">排名</div>
                    <div class="tableTitle flex-1 text-center">行业</div>
                    <div class="tableTitle flex-1 text-center">数量</div>
                    <div class="tableTitle flex-1 text-left pl-[18px]">占比</div>
                  </div>
                  <div class="h-[120px] overflow-y-hidden" @wheel.stop="onIndustryCategoryMousewheel">
                    <div
                      v-for="(v, i) in industryCategoriesForView"
                      :key="i + v.industrycategory"
                      class="flex items-center py-2 font-bold hover:bg-[RGBA(255,255,255,0.05)]"
                    >
                      <div class="tableTitle flex-1 text-center">{{ v.sort }}</div>
                      <div class="tableTitle flex-1 text-center">{{ v.industrycategory }}</div>
                      <div class="tableTitle flex-1 text-center">{{ v.count }}</div>
                      <div class="tableTitle flex-1 text-left pl-[18px]">{{ v.percent }}</div>
                    </div>
                  </div>
                </div>
                <div id="tradeCategories" class="w-full h-[13rem]"></div>
              </div>
            </div>
          </div>
        </template>
        <template v-else-if="tabIdSelected === 'projectOverview'">
          <div class="tabContentWrapper" key="projectOverview">
            <!--人员管控-->
            <div class="mt-[20px]">
              <img src="../assets/images/home/<USER>" alt="人员管控" />
              <div id="peopleManage" class="w-full h-40 mt-3" v-loading="isLoadingPeopleManageChartData"></div>
            </div>

            <!--设备设施-->
            <div class="mt-3">
              <img src="../assets/images/home/<USER>" class="w-[20.375rem] h-[1.75rem]" alt="设备设施" />
              <div class="relative h-40 mt-3" v-loading="isLoadingEquitmentCount">
                <img class="absolute h-[90%] left-[48px] top-[10px]" src="../assets/images/home/<USER>" alt="在场设备" />
                <div style="transform: translate3d(calc(-50% - 16px), -50%, 0)" class="absolute left-[50%] top-[50%] flex flex-col justify-center items-center">
                  <div class="font-ding text-4xl">
                    {{ availableEquipmentInfo.total }}
                  </div>
                  <div class="text-lg">在场设备</div>
                </div>
                <div class="absolute top-[2px] right-[64px]">
                  <div class="font-ding text-[20px]">
                    {{ availableEquipmentInfo.details[1]?.count }}
                  </div>
                  <div class="text-[14px]">
                    {{ availableEquipmentInfo.details[1]?.name }}
                  </div>
                </div>
                <div class="absolute bottom-0 right-[34px]">
                  <div class="font-ding text-[20px]">
                    {{ availableEquipmentInfo.details[2]?.count }}
                  </div>
                  <div class="text-[14px]">
                    {{ availableEquipmentInfo.details[2]?.name }}
                  </div>
                </div>
                <div class="absolute top-[58px] left-[32px]">
                  <div class="font-ding text-[20px]">
                    {{ availableEquipmentInfo.details[0]?.count }}
                  </div>
                  <div class="text-[14px]">
                    {{ availableEquipmentInfo.details[0]?.name }}
                  </div>
                </div>
              </div>
            </div>

            <!--模架装备-->
            <div v-if="!projectIdsWithoutMoldBase.includes(currentProjectId)" class="mt-3">
              <img src="../assets/images/home/<USER>" class="w-[20.375rem] h-[1.75rem]" alt="模架装备" />
              <div class="relative mt-3 w-full h-[168px]" v-loading="isLoadingVideoList">
                <iframe class="w-full h-full rounded-tl-[6px] rounded-tr-[6px]" :src="videoUrl" allowfullscreen frameborder="no" scrolling="no"> </iframe>
                <div class="absolute z-50 top-3 right-3 rounded-sm inline-flex justify-center items-center cursor-pointer">
                  <drop-down
                    :options="videoUrlList"
                    :selectedId="videoUrlId"
                    @change="onVideoUrlChange"
                    :withIcon="false"
                    :withText="false"
                    :withCheck="false"
                    :style="{ width: '144px', height: '34px' }"
                  />
                </div>
              </div>
            </div>

            <!--环境影响-->
            <div class="mt-3">
              <img src="../assets/images/home/<USER>" class="w-[20.375rem] h-[1.75rem]" alt="环境影响" />
              <div class="mt-3 py-3 pl-4" v-loading="isLoadingEnvEffect">
                <div class="flex justify-between items-center">
                  <div class=""> {{ envMeasurePointInfo?.pointname }}&nbsp;&nbsp;{{ envMeasurePointInfo?.pointcode }} </div>
                  <div class="flex items-center space-x-3" :class="[{ 'cursor-not-allowed': envMeasurePointInfoList.length < 2 }]">
                    <div
                      class="w-7 h-7 rounded-[8px] bg-[#545975] p-2 cursor-pointer"
                      @click="onEnvMeasureNavClick('prev')"
                      :class="[{ 'cursor-not-allowed': envMeasurePointIndex === 0 }]"
                    >
                      <img src="../assets/images/home/<USER>" class="rotate-180" alt="上一个" />
                    </div>
                    <div
                      class="w-7 h-7 rounded-[8px] bg-[#545975] p-2 cursor-pointer"
                      @click="onEnvMeasureNavClick('next')"
                      :class="[{ 'cursor-not-allowed': envMeasurePointIndex === envMeasurePointInfoList.length - 1 }]"
                    >
                      <img src="../assets/images/home/<USER>" alt="下一个" />
                    </div>
                  </div>
                </div>
                <div class="mt-4 grid grid-cols-3 gap-x-[48px] gap-y-4">
                  <div class="flex items-center">
                    <img class="w-9" src="../assets/images/home/<USER>" alt="温度" />
                    <div class="ml-2">
                      <div class="text-[12px]">温度</div>
                      <div>
                        <span class="font-ding text-lg">{{ envMeasurePointInfo?.temperature }}</span>
                        <span class="ml-2 text-[12px]">°C</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <img class="w-9" src="../assets/images/home/<USER>" alt="湿度" />
                    <div class="ml-2">
                      <div class="text-[12px]">湿度</div>
                      <div>
                        <span class="font-ding text-lg">{{ envMeasurePointInfo?.humidity }}</span>
                        <span class="ml-2 text-[12px]">%</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <img class="w-9" src="../assets/images/home/<USER>" alt="噪音" />
                    <div class="ml-2">
                      <div class="text-[12px]">噪音</div>
                      <div>
                        <span class="font-ding text-lg">{{ envMeasurePointInfo?.noise }}</span>
                        <span class="ml-2 text-[12px]">dB</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <img class="w-9" src="../assets/images/home/<USER>" alt="PM2.5" />
                    <div class="ml-2">
                      <div class="text-[12px]">PM2.5</div>
                      <div>
                        <span class="font-ding text-lg">{{ envMeasurePointInfo?.pm2_5 }}</span>
                        <span class="ml-2 text-[12px]">ug/m³</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <img class="w-9" src="../assets/images/home/<USER>" alt="PM10" />
                    <div class="ml-2">
                      <div class="text-[12px]">PM10</div>
                      <div>
                        <span class="font-ding text-lg">{{ envMeasurePointInfo?.pm10 }}</span>
                        <span class="ml-2 text-[12px]">ug/m³</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </collapse>

    <collapse
      direction="bottom"
      class="absolute z-50 bottom-2 left-[50%] -translate-x-[50%] projectInfoLoadingFlag"
      bgc="#2a2a41"
      v-loading="isLoadingProjectInfo"
    >
      <project-info :isLoading="isLoadingProjectInfo" :projectInfo="projectInfo" :environmentInfo="environmentInfo" />
    </collapse>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  import ProjectInfo from "../components/ProjectInfo.vue";
  import DropDown from "../components/DropDown.vue";
  import { HomeAPI } from "../api/home";
  import { EnvEffectAPI } from "../api/envEffect";

  const videoPointTemplate2 = `
      <div class="absolute top-0 left-full ml-[2px] w-[450px] h-[315px] bg-[#151515] rounded-[6px] p-[12px] cursor-auto">
        <div class="flex justify-between">
          <div class="font-pingfang">
            <div class="text-[14px]">#videoname#</div>
            <div class="text-[#fff] text-opacity-30 text-[12px]">编号:&nbsp;#device_serial#</div>
          </div>
          <img class="mt-[6px] cursor-pointer isClose w-[12px] h-[12px]" src="/images/env/close.png" alt="关闭" />
        </div>
        <iframe
          class="mt-[6px] w-full h-[244px] rounded-tl-[6px] rounded-tr-[6px]"
          src="https://open.ys7.com/ezopen/h5/iframe?url=#videourl#" allowfullscreen frameborder="no">
        </iframe>
      </div>
  `;

  export default {
    name: "Home",
    components: {
      "project-info": ProjectInfo,
      "drop-down": DropDown,
    },
    data() {
      return {
        tabs: [
          {
            id: "groupOverview",
            label: "集团总览",
          },
          {
            id: "projectOverview",
            label: "项目概况",
          },
        ],
        tabIdSelected: "groupOverview",

        isLoadingTotalGoing: false, //集团总览-总开工数
        totalGoingData: {},

        isLoadingProjectTypeSummary: false, //集团总览-工程类型汇总

        isLoadingTradeCategoriesStatistics: false, //集团总览-行业分类统计
        rankData: [],
        rankItemStartIndex: 0,
        rankItemCountForView: 3,

        isLoadingPeopleManageChartData: false, //项目概况-人员管控

        isLoadingEquitmentCount: false, //项目概况-设备设施
        equipmentList: [],

        isLoadingVideoList: false, //项目概况-模架装备
        videoUrlList: [],
        videoItem: null,

        isLoadingEnvEffect: false, //项目概况-环境影响
        envMeasurePointInfoList: [],
        envMeasurePointIndex: -1,

        chartInstances: [], //echart实例数组

        isLoadingProjectInfo: false, //建筑信息、当前进度
        projectInfo: {},
        environmentInfo: {},

        cameras: [], //摄像头设备数组
        selectedCameraId: "", //选中的摄像头id
        isCamerAnchorsAdded: false, //摄像头锚点添加标识
      };
    },
    computed: {
      availableEquipmentInfo() {
        const types = ["围挡", "塔吊", "升降机"];
        const result = { total: 0, details: [] };
        const { equipmentList } = this;
        types.forEach((type) => {
          const item = equipmentList.find((item) => item.name === type);
          if (item) {
            result.total += item.count;
            result.details.push(item);
          } else {
            result.details.push({
              name: type,
              count: 0,
            });
          }
        });
        return result;
      },
      envMeasurePointInfo() {
        return this.envMeasurePointInfoList[this.envMeasurePointIndex];
      },
      ...mapGetters("common", ["currentProjectId", "projectTypeColorMap", "workerCategoryColorMap", "projectIdsWithoutMoldBase", "currentProjectRegionCode"]),
      availableCameras() {
        return this.cameras[0]?.details ?? []; //第四区
      },
      videoUrlId() {
        return this.videoItem?.id ?? "";
      },
      videoUrl() {
        const url = this.videoItem?.url;
        if (url) {
          return `https://open.ys7.com/ezopen/h5/iframe?url=${url}`;
        } else {
          return "";
        }
      },
      rankDataMaxIndex() {
        const len = this.rankData?.length;
        if (len) {
          if (len < this.rankItemCountForView) {
            return len;
          } else {
            return len - this.rankItemCountForView;
          }
        } else {
          return 0;
        }
      },
      industryCategoriesForView() {
        return this.rankData?.slice(this.rankItemStartIndex, this.rankItemStartIndex + this.rankItemCountForView) ?? [];
      },
      selectedCamera() {
        return this.availableCameras.find((item) => item?.id === this.selectedCameraId);
      },
    },
    watch: {
      currentProjectId: {
        immediate: true,
        handler(cv) {
          cv && this.updateThePage();
        },
      },
    },
    mounted() {
      this.exposeToWindow();
    },
    beforeDestroy() {
      this.disposeTheCharts();
      this.removeCameraAnchors();
      this.deleteExposeToWindow();
    },
    methods: {
      //更新页面
      updateThePage() {
        this.setCameras().then(this.addVideoAnchors);
        this.disposeTheCharts();
        this.updateProjectInfo();
        this.updateWeatherInfo();
        this.$nextTick(this.updateSelectedTab);
      },
      //释放echrts图表
      disposeTheCharts() {
        this.chartInstances.forEach((item) => {
          item.dispose();
        });
        this.chartInstances = [];
      },

      //响应行业分类统计滚轮事件
      onIndustryCategoryMousewheel(e) {
        const { deltaY } = e;
        let currentRankItemStartIndex = this.rankItemStartIndex;
        if (deltaY > 0) {
          if (++currentRankItemStartIndex > this.rankDataMaxIndex) {
            this.rankItemStartIndex = this.rankDataMaxIndex;
            return;
          }
          this.rankItemStartIndex++;
        } else {
          if (--currentRankItemStartIndex < 0) {
            this.rankItemStartIndex = 0;
            return;
          }
          this.rankItemStartIndex--;
        }
      },
      //建筑信息、当前进度
      async updateProjectInfo() {
        this.isLoadingProjectInfo = true;
        this.projectInfo = null;
        const res = await HomeAPI.getProjectInfo({
          projectId: this.currentProjectId,
          token: this.token,
        });
        this.isLoadingProjectInfo = false;
        this.projectInfo = res?.code === 0 ? res.data : null;
      },
      async updateWeatherInfo() {
        const res = await this.$http.get(`https://api.help.bj.cn/apis/weather/?id=${this.currentProjectRegionCode}`, {
          baseURL: "/",
          withProjectId: false,
          withToken: false,
        });
        this.environmentInfo = res?.code === 0 ? res.data : null;
      },
      //更新选中的页签
      updateSelectedTab() {
        //prettier-ignore
        switch (this.tabIdSelected) {
          case "groupOverview": { this.updateGroupOverviewTab(); return; }
          case "projectOverview": { this.updateProjectOverviewTab(); return; }
        }
      },
      //响应tab页签点击事件
      onTabClick(item) {
        if (item.id === this.tabIdSelected) return;
        this.disposeTheCharts();
        this.tabIdSelected = item.id;
        this.$nextTick(this.updateSelectedTab);
      },

      //集团总览
      async updateGroupOverviewTab() {
        //todo 调用新的接口组装数据源
        this.updateTotalGoing();
        this.updateProjectTypeSummary();
        this.updateTradeCategoriesStatistics();
      },
      //项目概况
      updateProjectOverviewTab() {
        this.updateWorkerCategoryChart();
        this.updateEquipmentCount();
        this.updateVideoInfo(8);
        this.updateEvnPointInfo();
      },

      //集团总览-总开工数
      async updateTotalGoing() {
        this.isLoadingTotalGoing = true;
        const res = await HomeAPI.getTotalGoingData();
        this.isLoadingTotalGoing = false;
        this.totalGoingData = res?.code === 0 ? res.data : {};
        this.showTotalGoingChart(this.totalGoingData.items);
      },
      async showTotalGoingChart(items) {
        if (items?.length) {
          const chartDom = document.getElementById("totalGoing");
          if (chartDom) {
            const myChart = this.$echarts.init(chartDom);
            const option = {
              tooltip: {
                trigger: "axis",
              },
              xAxis: {
                type: "category",
                boundaryGap: true,
                data: items.map((item) => item.orgname),
                axisTick: { show: false },
                axisLine: { show: false },
                axisLabel: {
                  show: true,
                  color: "#fff",
                  fontSize: 14,
                },
              },
              yAxis: {
                type: "value",
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
              },
              grid: {
                top: 0,
                bottom: 24,
                left: 14,
                right: 0,
              },
              series: [
                {
                  type: "line",
                  showSymbol: false,
                  data: items.map((item) => item.count),
                  areaStyle: {
                    opacity: 0.8,
                    color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "rgba(255, 97, 77, 0.3)",
                      },
                      {
                        offset: 1,
                        color: "rgba(255, 97, 77, 0)",
                      },
                    ]),
                  },
                  lineStyle: {
                    width: 2,
                    color: "rgba(255, 97, 77, 1)",
                  },
                },
              ],
              dataZoom: [{ type: "inside" }],
            };
            myChart.setOption(option);
            this.chartInstances.push(myChart);
          }
        }
      },

      //集团总览-工程类型汇总
      async updateProjectTypeSummary() {
        this.isLoadingProjectTypeSummary = true;
        const res = await HomeAPI.getProjectTypeSummaryData();
        this.isLoadingProjectTypeSummary = false;
        this.showProjectTypeSummaryChart(res?.code === 0 ? res.data : null);
      },
      showProjectTypeSummaryChart(items) {
        if (items?.length) {
          const chartDom = document.getElementById("projectTypeSummary");
          if (chartDom) {
            const myChart = this.$echarts.init(chartDom);
            items.sort((a, b) => b.count - a.count); //倒序排序
            let maxNum = Math.max(...items.map((item) => item.count));
            maxNum = maxNum + maxNum * 0.1; //产生invisible
            items = items.map((item) => {
              return {
                name: item.projecttype,
                num: item.count,
                value: item.count / maxNum,
              };
            });
            function genSeries(data) {
              // console.log("ddd", data);
              return data.map((item, i) => {
                return {
                  type: "pie",
                  clockwise: false, // 顺时加载
                  emphasis: { scale: true },
                  radius: [80 - i * 10, 70 - i * 10],
                  center: ["28%", "50%"],
                  label: { show: false },
                  labelLine: { show: false },
                  data: [
                    {
                      value: item.value,
                      name: item.name,
                      num: item.num,
                      tooltip: {
                        show: true,
                        formatter: function (params) {
                          const { data } = params;
                          return `${data?.name ?? ""} ${data?.num ?? ""} (${params.percent ?? ""}%)`;
                        },
                      },
                    },
                    {
                      value: 1 - item.value,
                      name: `${item.name}-invisible`,
                      itemStyle: { color: "rgba(0,0,0,0)" },
                      label: { show: false },
                      labelLine: { show: false },
                      emphasis: { color: "rgba(0,0,0,0)" },
                      tooltip: {
                        show: false,
                        // formatter: function (params) {
                        //   const { data } = params;
                        //   return `${data?.name ?? ""} ${data?.num ?? ""} (${params.percent ?? ""}%)`;
                        // },
                      },
                    },
                  ],
                };
              });
            }
            const option = {
              tooltip: { trigger: "item" },
              color: items.map((item) => this.projectTypeColorMap[item.name]),
              legend: {
                icon: "circle",
                itemGap: 10,
                orient: "vertical",
                top: 15,
                bottom: 0,
                right: 0,
                data: items,
                textStyle: {
                  rich: {
                    a: {
                      color: "#fff",
                      width: 50,
                    },
                    b: { color: "#fff" },
                  },
                },
                formatter: function (name) {
                  const item = items.find((item) => item.name === name);
                  return `{a|${item.name}} {b|${item.num}}`;
                },
              },
              series: genSeries(items),
            };
            myChart.setOption(option);
            this.chartInstances.push(myChart);
          }
        }
      },

      //集团总览-行业分类统计
      async updateTradeCategoriesStatistics() {
        this.isLoadingTradeCategoriesStatistics = true;
        const res = await HomeAPI.getTradeCategoriesStatisticsData();
        this.isLoadingTradeCategoriesStatistics = false;
        const { data: chartData } = res?.code === 0 ? res.data : { data: [] };
        // this.rankData = rankData?.sort((a, b) => b.count - a.count);
        const totalCount = chartData.reduce((acc, next) => {
          return acc + Number(next?.count ?? 0);
        }, 0);
        this.rankData = chartData.map((item, index) => {
          return {
            ...item,
            sort: index + 1,
            percent: (Number(item.count / totalCount) * 100).toFixed(2) + "%",
          };
        });
        this.showTradeCategoriesChart(chartData);
      },
      showTradeCategoriesChart(items) {
        if (items?.length) {
          const chartDom = document.getElementById("tradeCategories");
          if (chartDom) {
            const myChart = this.$echarts.init(chartDom);
            const data = items.map((item) => item.count);
            const yMax = Math.max(...data);
            const dataShadow = [];
            for (let i = 0; i < data.length; i++) {
              dataShadow.push({
                value: yMax,
                name: data[i],
              });
            }
            const option = {
              grid: {
                top: 16,
                left: 8,
                right: 0,
                bottom: 24,
              },
              xAxis: {
                type: "category",
                data: items.map((item) => item.industrycategory),
                axisLabel: {
                  show: true,
                  inside: false,
                  color: "#fff",
                  fontSize: 14,
                  fontWeight: "bold",
                },
                axisLine: { show: false },
                splitLine: { show: false },
                axisTick: { show: false },
                z: 10,
                boundaryGap: true,
              },
              yAxis: {
                type: "value",
                axisLabel: { show: false },
                splitLine: { show: false },
              },
              dataZoom: [{ type: "inside" }],
              series: [
                {
                  type: "bar",
                  itemStyle: {
                    color: "rgba(253,197,115,0.1)",
                    borderRadius: 10,
                  },
                  barCategoryGap: "40%",
                  barWidth: 10,
                  barGap: "-100%",
                  label: {
                    show: true,
                    position: "top",
                    color: "#fff",
                    formatter: function (params) {
                      return params.name;
                    },
                  },
                  data: dataShadow,
                  animation: false,
                },
                {
                  type: "bar",
                  itemStyle: {
                    color: "rgba(253,197,115,1)",
                    borderRadius: 10,
                  },
                  barCategoryGap: "40%",
                  barWidth: 10,
                  emphasis: {
                    itemStyle: {
                      color: "rgba(255,255,203,1)",
                    },
                  },
                  data: data,
                },
              ],
            };
            myChart.setOption(option);
            this.chartInstances.push(myChart);
          }
        }
      },

      //项目概况-人员管控
      async updateWorkerCategoryChart() {
        this.isLoadingPeopleManageChartData = true;
        const res = await HomeAPI.getWorkerCategoryData({
          token: this.token,
          projectId: this.currentProjectId,
        });
        this.isLoadingPeopleManageChartData = false;
        res?.code === 0 && this.showWorkerCategoryChart(res.data);
      },
      showWorkerCategoryChart(data) {
        if (data?.length) {
          const chartDom = document.getElementById("peopleManage");
          if (chartDom) {
            const myChart = this.$echarts.init(chartDom);
            const dataSource = data.reduce((acc, next) => {
              acc[next.name] = next.count;
              return acc;
            }, {});
            const option = {
              tooltip: { trigger: "item" },
              legend: {
                orient: "vertical",
                right: 0,
                top: 24,
                itemGap: 12,
                textStyle: {
                  rich: {
                    a: {
                      color: "#fff",
                      fontSize: 14,
                      width: 90,
                    },
                    b: { color: "#fff" },
                  },
                },
                formatter: function (name) {
                  return `{a|${name}} {b|${dataSource[name]}}`;
                },
              },
              series: [
                {
                  type: "pie",
                  radius: ["30%", "100%"],
                  center: ["30%", "45%"],
                  top: 24,
                  right: 0,
                  roseType: "radius",
                  itemStyle: {
                    borderRadius: 2,
                    shadowBlur: 100,
                    shadowColor: "rgba(0,0,0,0.5)",
                  },
                  label: { show: false },
                  data: Object.keys(dataSource).map((item) => {
                    return {
                      value: dataSource[item],
                      name: item,
                      itemStyle: {
                        color: this.workerCategoryColorMap[item],
                      },
                    };
                  }),
                },
              ],
            };
            myChart.setOption(option);
            this.chartInstances.push(myChart);
          }
        }
      },

      //项目概况-设备设施
      async updateEquipmentCount() {
        if (this.projectIdsWithoutMoldBase.includes(this.currentProjectId)) {
          return;
        }
        this.isLoadingEquitmentCount = true;
        const res = await HomeAPI.getEquipmentCategoryCount({
          token: this.token,
          projectId: this.currentProjectId,
        });
        this.isLoadingEquitmentCount = false;
        res?.code === 0 && (this.equipmentList = res.data);
      },

      //项目概况-模架装备
      //项目概况-模架装备,视频下拉框
      async updateVideoInfo(areaId) {
        this.videoItem = null;
        this.isLoadingVideoList = true;
        // const res = await HomeAPI.getVideoByAreaId({ id: areaId });
        const res = await HomeAPI.getVideoSourceList(); // /beh/person/tree
        this.videoUrlList = (res?.code === 0 ? res.data : [])
          .filter((item) => item.name === "摄像头")?.[0]
          ?.children?.map((item) => {
            return {
              ...item,
              id: item.id + "",
              label: item.name,
            };
          }); //从结果集当中取出摄像头的数据;
        this.isLoadingVideoList = false;
        this.videoItem = this.videoUrlList[0] ?? null;
      },
      //响应下拉框改变事件
      onVideoUrlChange(item) {
        this.videoItem = item;
      },

      //项目概况-环境影响
      async updateEvnPointInfo() {
        this.isLoadingEnvEffect = true;
        const res = await HomeAPI.getEnvPointInfo({ withToken: false });
        this.isLoadingEnvEffect = false;
        this.envMeasurePointInfoList = res?.code === 0 ? res.data : [];

        if (this.envMeasurePointInfoList?.length) {
          this.envMeasurePointIndex = 0;
        } else {
          this.envMeasurePointIndex = -1;
        }
      },
      onEnvMeasureNavClick(dir) {
        if (dir === "prev") {
          if (this.envMeasurePointIndex <= 0) {
            this.envMeasurePointIndex = 0;
            return;
          }
          this.envMeasurePointIndex--;
        } else if (dir === "next") {
          const max = this.envMeasurePointInfoList.length - 1;
          if (this.envMeasurePointIndex >= max) {
            this.envMeasurePointIndex = max;
            return;
          }
          this.envMeasurePointIndex++;
        }
      },

      //暴露接口到全局环境
      exposeToWindow() {
        window.page = this;
      },
      //移除暴露的接口
      deleteExposeToWindow() {
        window.page = null;
      },

      //加入摄像头锚点
      addVideoAnchors() {
        if (!this.isCamerAnchorsAdded) {
          if (window.scene) {
            console.log("加入摄像头锚点");
            const availableCameras = this.availableCameras; //第四区
            if (availableCameras.length) {
              this.addVideoDataAnnotation([121.429973875163, 31.196811931116372, 0], availableCameras[0]);
              this.addVideoDataAnnotation([121.43097794508458, 31.196045724850194, 0], availableCameras[1]);
              this.addVideoDataAnnotation([121.42857433934881, 31.19573011866062, 0], availableCameras[2]);
              this.isCamerAnchorsAdded = true;
            }
          }
        }
      },
      //添加视频监控二维标签
      addVideoDataAnnotation(geographical, arg) {
        const scene = window.scene;
        if (scene) {
          // position 和altitude
          //根据元素的坐标、地理位置,生成THREE.Box3包围盒,判断包围盒是否相交
          const annotation = scene.addFeature("annotation", arg.id);
          annotation.origin = [geographical[0], geographical[1]];
          annotation.altitude = geographical[2];
          const data = {
            position: { x: 0, y: 0, z: 0 }, //偏移量
            // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
            content: null,
            // 二维标签html，自定义html字符串
            htmlCode: `
              <div data-id='${arg.id}' id='content-${arg.id}' class="videoPointClass relative flex items-center justify-center w-[34px] h-[34px]  border border-[#FFF] rounded-full">
                <img data-id='${arg.id}' class="isCameraIcon w-[14px] h-[14px]" src="/anchorIcons/camera.png">
                <div class="absolute top-[40px] space-y-[6px]">
                  <div class="w-[8px] h-[8px] rounded-full bg-[#FFF]"></div>
                  <div class="w-[8px] h-[8px] rounded-full bg-[#FFF]"></div>
                  <div class="w-[8px] h-[8px] rounded-full bg-[#FFF]"></div>
                  <div class="w-[8px] h-[8px] rounded-full bg-[#FFF]"></div>
                  <div class="absolute left-[50%] w-[60px] h-[60px] origin-center rounded-full border border-[rgba(255,255,255,0.1)]"
                    style="background:radial-gradient(circle farthest-corner at center center, transparent 0, transparent 8px, #FFF 9px, #FFF 9px, transparent 10px, transparent 18px, rgba(255,255,255,0.3) 19px,rgba(255,255,255,0.3) 19px,transparent 20px);
                    transform:translate3d(-50%,-36px,0) rotate3d(1,0,0,56deg)"
                  >
                  </div>
                </div>
                <div id="videoWrapper-${arg.id}"></div>
              </div>
            `,
            // 二维标签样式设置，自定义css字符串
            // 如果二次添加的锚点样式相同，该参数可为空
            cssCode: `
            .videoPointClass{background-color: #4D64E1;}
            .videoPointClass.activated{background-color: #FB5101;}`,
            // 二维标签js，自定义字符串
            //增加一个window.page里面包含可以公用的方法
            jsCode: `annotation.addEventListener("click",function(e){
            const target = e?.target??null;
            if(target){
              const targetClassList = target.classList;
              if(targetClassList.contains("videoPointClass")||targetClassList.contains("isCameraIcon")){
                window.page?.onVideoPointClick(null,target.dataset.id);
              }else if(targetClassList.contains("isClose")){
                window.page?.resetAllVideoPoint();
              }
            }
            });`,
            // 自定义锚点类型，默认为default,自定义标签为custom
            setType: { anchorType: "custom" },
            visibleDistance: 1500,
          };
          // 二维标签数据标识
          annotation.dataKey = "annotation-" + annotation.id;
          // 设置标签数据
          scene.postData(data, "annotation-" + annotation.id);
          // 加载二维标签
          annotation.load();
        }
      },

      //设置摄像机数据为和环境影响-视频监控一样的数据源
      async setCameras() {
        this.cameras = [];
        const res = await EnvEffectAPI.getVideoList();
        this.cameras = (res?.code === 0 ? res.data : []).map((item, index) => {
          return {
            groupId: index,
            groupName: item.areaname,
            details: item.videos.map((item, index) => {
              return {
                ...item,
                videoname: `${index + 1}#摄像头`,
                id: `video-${this.$nanoid()}`,
              };
            }),
          };
        });
      },

      //响应视频监控点击事件
      onVideoPointClick(detail, id) {
        if (detail || id) {
          if (detail?.id === this.selectedCameraId || id === this.selectedCameraId) return;
          this.resetAllVideoPoint();
          this.selectedCameraId = id;
          this.fit2Feature(id);
          let targetDom = document.getElementById(`content-${id}`);
          if (targetDom) {
            targetDom.classList.add("activated");
            targetDom = document.getElementById(`videoWrapper-${id}`);
            const { videoname, deviceserial, videourl } = detail ?? this.selectedCamera;
            targetDom.innerHTML = videoPointTemplate2
              .replace("#videoname#", videoname)
              .replace("#device_serial#", deviceserial)
              .replace("#videourl#", videourl);

            targetDom = document.getElementById(id);
            if (targetDom) {
              const currentIndex = Number(targetDom.style.zIndex);
              targetDom.style.zIndex = currentIndex + 1;
            }
          }
        }
      },
      //将目标元素聚焦到画布中央
      fit2Feature(id) {
        const scene = window.scene;
        if (scene) {
          const target = scene.findFeature(id);
          target && scene.fit2Feature(target, 1, true);
        }
      },
      //重置视频测点
      resetAllVideoPoint() {
        const targets = document.querySelectorAll(".videoPointClass");
        targets.forEach((item) => {
          this.restoreOriginalZIndex(item);
          item.classList.remove("activated");
          if (item.children[2]) {
            item.children[2].innerHTML = "";
          }
        });
        this.selectedCameraId = "";
      },
      //还原z-index
      restoreOriginalZIndex(item) {
        const target = item.parentNode;
        if (target) {
          const currentIndex = Number(target.style.zIndex);
          if (currentIndex > 9) {
            target.style.zIndex = currentIndex - 1;
          }
        }
      },
      //移除摄像头锚点
      removeCameraAnchors() {
        console.log("移除摄像头锚点");
        if (window.scene) {
          const reg = /^\s*video-/i;
          const scene = window.scene;
          scene.features.forEach((feature) => {
            if (reg.test(feature?.id + "")) {
              scene.removeFeature(feature.id, true);
            }
          });
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .tabsWrapper {
    background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9) 0%, hsla(236, 33%, 18%, 0.9) 100%) no-repeat center/cover border-box;
  }
  ::v-deep .projectInfoLoadingFlag .el-loading-mask {
    @apply rounded-[16px];
  }
  .tab-item.activated {
    @apply rounded-tl-[1rem] rounded-br-[1rem] bg-[#2849CD];
    box-shadow: -1px 0px 25px 0px rgba(14, 15, 15, 0.1);
  }
  .titleBg {
    background: url("/images/title-bg.png") no-repeat left center / cover border-box;
  }
</style>
