<template>
  <div class="viewWrapper">
    <collapse direction="right" class="absolute z-50 right-0 top-[127px]">
      <div class="viewMainBG w-[520px] px-[24px] pt-[20px] rounded-tl-[12px] rounded-bl-[12px]" key="verticalDeformation">
        <div class="inline-flex ml-[6px] space-x-[16px] font-pingfang-regular text-[18px]" key="2">
          <div>竖向预变形控制</div>
          <div>Vertival pre-deformation control</div>
        </div>

        <!--控制点预变形补偿值图表-->
        <div class="mt-2">
          <div class="titleBG inline-flex mt-[22px] ml-[8px] space-x-[8px] h-[34px] px-[14px] items-center font-pingfang-regular text-[16px]">
            <div>控制点预变形补偿值图表</div>
            <div>mm</div>
          </div>
          <div class="flex mt-[32px] w-full h-[156px]" v-loading="isLoadingTemperatureData">
            <div id="preDeformationId" class="-mt-[32px] flex-1"></div>
          </div>
        </div>

        <!--控制点高程偏差预测-->
        <div class="mt-6">
          <div class="titleBG inline-flex mt-[22px] ml-[8px] space-x-[8px] h-[34px] px-[14px] items-center font-pingfang-regular text-[16px]">
            <div>控制点高程偏差预测</div>
            <div>mm</div>
          </div>
          <div class="flex mt-[32px] w-full h-[156px]" v-loading="isLoadingPressureData">
            <div id="controlPointId" class="-mt-[32px] flex-1"></div>
          </div>
        </div>

        <!--横向支撑节点应变监测-->
        <div class="mt-6">
          <div class="titleBG inline-flex mt-[22px] ml-[8px] space-x-[8px] h-[34px] px-[14px] items-center font-pingfang-regular text-[16px]">
            <div>横向支撑节点应变监测</div>
            <div>με</div>
          </div>
          <div class="flex mt-[32px] w-full h-[200px]" v-loading="isLoadingSpeed">
            <div id="supportNodeId" class="-mt-[32px] flex-1"></div>
          </div>
        </div>
      </div>
    </collapse>
  </div>
</template>

<script>
  import { VerticalDeformationAPI } from "../api/verticalDeformation.js";
  import { mapGetters } from "vuex";

  export default {
    name: "VerticalDeformation",
    data() {
      return {
        chartInstances: [],
        temperatureData: {
          items: null,
          chartData: null,
        },
        isLoadingTemperatureData: false,

        pressureeData: {
          items: null,
          chartData: null,
        },
        isLoadingPressureData: false,

        legendNames: ["1#", "2#", "3#", "4#"],

        speedData: {
          items: null,
          chartData: null,
        },
        isLoadingSpeed: false,
      };
    },
    computed: {
      ...mapGetters("common", ["currentProjectId", "isSceneLoadComplete", "concretePumpPerformanceColorMap"]),
    },
    watch: {
      currentProjectId: {
        immediate: true,
        handler(cv) {
          cv && this.updateThePage();
        },
      },
      isSceneLoadComplete: {
        immediate: true,
        handler(cv) {
          // console.log("watch-isSceneLoadComplete,vertical");
          if (cv) {
            this.addSupportPoints();
          }
        },
      },
    },
    mounted() {
      this.exposeToWindow();
    },
    beforeDestroy() {
      this.removeLegendSelectChangedListener();
      this.disposeTheCharts();
      this.removeSupportPoints();
      this.deleteExposeToWindow();
    },
    methods: {
      addSupportPoints() {
        this.addHintAnnotation([121.4294495335642, 31.195437089493723, 76.25019763378782], { id: `support-${this.$nanoid()}`, num: 1 });
        this.addHintAnnotation([121.42901484196142, 31.195234071012578, 76.25019763378782], { id: `support-${this.$nanoid()}`, num: 2 });
        this.addHintAnnotation([121.42910392342799, 31.19490193140092, 76.25019763378782], { id: `support-${this.$nanoid()}`, num: 3 });
        this.addHintAnnotation([121.42964159443159, 31.195072103596033, 76.25019763378782], { id: `support-${this.$nanoid()}`, num: 4 });
      },
      removeSupportPoints() {
        if (window.scene) {
          const reg = /^\s*support-/i;
          const scene = window.scene;
          window.scene.features.forEach((feature) => {
            if (reg.test(feature?.id + "")) {
              scene.removeFeature(feature.id, true);
            }
          });
        }
      },
      //释放echart实例
      disposeTheCharts() {
        this.chartInstances.forEach((item) => {
          item.dispose();
        });
        this.chartInstances = [];
      },

      //更新页面
      updateThePage() {
        this.disposeTheCharts();
        this.updatePreDeformationData();
        this.updateControlPointData();
        this.updateSupportNodeData();
      },

      //控制点预变形补偿值图表
      async updatePreDeformationData() {
        this.isLoadingTemperatureData = true;
        let res = await VerticalDeformationAPI.getPreDeformationData();
        this.isLoadingTemperatureData = false;
        const data = res?.code === 0 ? res.data : null;
        const chartDom = document.getElementById("preDeformationId");
        if (chartDom) {
          const myChart = this.$echarts.init(chartDom);
          const xData = data.map((item) => item[0]);
          const option = {
            color: "#ffcc75",
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
              valueFormatter(value) {
                return `${value} mm`;
              },
            },
            grid: {
              top: 0,
              bottom: 18,
              left: 36,
              right: 0,
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: xData,
                axisTick: { show: false },
                axisLine: { show: false },
                axisLabel: {
                  fontSize: 12,
                  color: "hsla(219, 52%, 81%, 0.5)",
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                show: true,
                axisTick: { show: false },
                splitLine: { show: false },
                splitNumber: 3,
                axisLabel: {
                  verticalAlign: "bottom",
                  fontSize: 12,
                  color: "hsla(219, 52%, 81%, 0.5)",
                },
              },
            ],
            series: {
              type: "line",
              smooth: true,
              showSymbol: false,
              lineStyle: {
                width: 3,
              },
              areaStyle: {
                opacity: 0.9,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#3b3337",
                  },
                  {
                    offset: 1,
                    color: "#151624",
                  },
                ]),
              },
              data: data.map((item) => item[1]),
            },
            dataZoom: [{ type: "inside" }],
          };
          myChart.setOption(option);
          this.chartInstances.push(myChart);
        }
      },

      //控制点高程偏差预测图表
      async updateControlPointData() {
        this.isLoadingPressureData = true;
        let res = await VerticalDeformationAPI.getControlPointData();
        this.isLoadingPressureData = false;
        const data = res?.code === 0 ? res.data : null;
        const chartDom = document.getElementById("controlPointId");
        if (chartDom) {
          const myChart = this.$echarts.init(chartDom);
          const xData = data.map((item) => item[0]);
          const option = {
            color: "#ffcc75",
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
              valueFormatter(value) {
                return `${value} mm`;
              },
            },
            grid: {
              top: 0,
              bottom: 18,
              left: 36,
              right: 0,
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: xData,
                axisTick: { show: false },
                axisLine: { show: false },
                axisLabel: {
                  fontSize: 12,
                  color: "hsla(219, 52%, 81%, 0.5)",
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                show: true,
                axisTick: { show: false },
                splitLine: { show: false },
                splitNumber: 3,
                axisLabel: {
                  verticalAlign: "bottom",
                  fontSize: 12,
                  color: "hsla(219, 52%, 81%, 0.5)",
                },
              },
            ],
            series: {
              type: "line",
              smooth: true,
              showSymbol: false,
              lineStyle: {
                width: 3,
              },
              areaStyle: {
                opacity: 0.9,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#3b3337",
                  },
                  {
                    offset: 1,
                    color: "#151624",
                  },
                ]),
              },
              data: data.map((item) => item[1]),
            },
            dataZoom: [{ type: "inside" }],
          };
          myChart.setOption(option);
          this.chartInstances.push(myChart);
        }
      },

      //横向支撑节点应变监测图表
      async updateSupportNodeData() {
        this.isLoadingSpeed = true;
        let res = await VerticalDeformationAPI.getSupportNodeData();
        this.isLoadingSpeed = false;
        const values = res?.code === 0 ? res.data : null;
        const xDatas = [];
        const xDataEnd = new Date();
        xDataEnd.setFullYear(2017, 0, 1);
        xDataEnd.setHours(0, 0, 0, 0);

        const xDataStart = new Date();
        xDataStart.setFullYear(2014, 6, 1);
        xDataStart.setHours(0, 0, 0, 0);

        const timeSpan = 24 * 60 * 60 * 1000; //1天(毫秒)
        for (let len = (xDataEnd.getTime() - xDataStart.getTime()) / timeSpan; len >= 0; len--) {
          xDatas.push(xDataEnd - len * timeSpan);
        }
        const chartDom = document.getElementById("supportNodeId");
        if (chartDom) {
          const legendData = Object.keys(values).map((item) => {
            return {
              name: item,
              itemStyle: {
                color: this.concretePumpPerformanceColorMap[item],
              },
            };
          });
          const myChart = this.$echarts.init(chartDom);
          const that = this;
          const option = {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
              formatter(params) {
                return params
                  .map((item) => {
                    return `
                    <p>
                      <span style="display:inline-block;border-radius:10px;width:10px;height:10px;
                      background-color:${that.concretePumpPerformanceColorMap[item.seriesName]};"></span>
                      <span style="margin-left:4px">${item.seriesName}</span>
                      <span style="margin-left:4px">${that.$dayjs(Number(item.axisValue)).format("YYYY-MM-DD")}</span>
                      <span style="margin-left:4px">${item.value} με</span>
                    </p>`;
                  })
                  .join("<br/>");
              },
            },
            axisPointer: {
              label: {
                formatter(params) {
                  if (params.axisDimension === "x") {
                    return that.$dayjs(Number(params.value)).format("YYYY-MM-DD");
                  } else {
                    return params.value;
                  }
                },
              },
            },
            legend: {
              data: legendData,
              icon: "rect",
              top: 0,
              itemGap: 32,
              itemHeight: 2,
              itemWidth: 14,
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
            },
            grid: {
              top: 24,
              left: 36,
              bottom: 50,
              right: 0,
            },
            xAxis: {
              type: "category",
              data: xDatas,
              boundaryGap: true,
              axisTick: { show: false },
              axisLine: { show: false },
              axisLabel: {
                rotate: -20,
                fontSize: 12,
                color: "hsla(219, 52%, 81%, 0.5)",
                formatter(value) {
                  return that.$dayjs(Number(value)).format("YYYY-MM-DD");
                },
              },
            },
            yAxis: {
              type: "value",
              show: true,
              axisTick: { show: false },
              splitLine: { show: false },
              splitNumber: 3,
              axisLabel: {
                verticalAlign: "bottom",
                fontSize: 12,
                color: "hsla(219, 52%, 81%, 0.5)",
              },
            },
            dataZoom: [{ type: "inside" }],
            series: Object.keys(values).map((key) => {
              return {
                name: key,
                type: "line",
                smooth: true,
                showSymbol: false,
                itemStyle: { color: this.concretePumpPerformanceColorMap[key] },
                data: values[key],
              };
            }),
          };
          myChart.setOption(option);
          myChart.on("legendselectchanged", this.onLegendSelectChanged);
          this.chartInstances.push(myChart);
        }
      },

      //响应legend图例选中改变事件
      onLegendSelectChanged(data) {
        const name = data.name;
        const myChart = this.chartInstances?.[2];
        myChart?.dispatchAction({ type: "legendSelect", name });
        const target = document.querySelector(`[data-name='${name}'`);
        const id = target?.dataset?.id;
        this.fit2Feature(id);
      },

      //移除legend图例选中改变事件侦听器
      removeLegendSelectChangedListener() {
        const myChart = this.chartInstances?.[2];
        myChart?.off("legendselectchanged", this.onLegendSelectChanged);
      },

      //添加hint二维标签
      addHintAnnotation(geographical, arg) {
        const scene = window.scene;
        if (scene) {
          const annotation = scene.addFeature("annotation", arg.id);
          annotation.origin = [geographical[0], geographical[1]];
          annotation.altitude = geographical[2];
          const postData = {
            position: { x: 0, y: 0, z: 0 }, //偏移量
            // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
            content: null,
            // 二维标签html，自定义html字符串
            htmlCode: `<div data-name="${arg.num}#" data-id="${arg.id}" class="monitorPoint relative w-[34px] h-[34px] text-[16px] flex justify-center items-center">${arg.num}#</div>`,
            // 二维标签样式设置，自定义css字符串
            // 如果二次添加的锚点样式相同，该参数可为空
            cssCode: `.monitorPoint{ border-radius: 100%; background-color:#4d64e1; border: 1px solid #fff;}`,
            // 二维标签js，自定义字符串
            //增加一个window.page里面包含可以公用的方法
            jsCode: `annotation.addEventListener("click",function(e){
            window.page.showSpecifiedLine(e?.target?.dataset?.name);
            });`,
            // 自定义锚点类型，默认为default,自定义标签为custom
            setType: { anchorType: "custom" },
          };
          // 二维标签数据标识
          annotation.dataKey = "annotation-" + annotation.id;
          // 设置标签数据
          scene.postData(postData, annotation.dataKey);
          // 加载二维标签
          annotation.load();
        }
      },

      //展示特定横向支撑节点曲线
      showSpecifiedLine(name) {
        const myChart = this.chartInstances[2];
        const { legendNames } = this;
        if (myChart && name) {
          const targets = legendNames.filter((item) => item !== name);
          targets.forEach((item) => {
            myChart.dispatchAction({ type: "legendUnSelect", name: item });
          });
          myChart.dispatchAction({ type: "legendSelect", name });
        }
      },

      //将目标元素聚焦到画布中央
      fit2Feature(id) {
        const scene = window.scene;
        if (scene) {
          const target = scene.findFeature(id);
          target && scene.fit2Feature(target, 1, true);
        }
      },

      //暴露接口到全局环境
      exposeToWindow() {
        window.page = this;
      },

      //移除暴露的接口
      deleteExposeToWindow() {
        window.page = null;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .viewMainBG {
    background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9), hsla(236, 33%, 18%, 0.9)) center / contain no-repeat border-box;
  }
  .titleBG {
    background: linear-gradient(90deg, hsla(212, 26%, 50%, 0.4), hsla(211, 17%, 39%, 0.4), hsla(225, 11%, 7%, 0.4)) left center/contain no-repeat border-box;
  }
</style>
