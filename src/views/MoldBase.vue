<template>
  <div class="viewWrapper">
    <div
      class="legMonitorShadow absolute z-50 bg-[#545975] top-[120px] right-4 w-[188px] h-[42px] cursor-pointer rounded-tl-[12px] rounded-br-[12px] flex justify-between items-center"
    >
      <div class="text-lg pl-[24px]" @click="isLegMonitorDialogVisible = !isLegMonitorDialogVisible"> 油缸/牛腿监控 </div>
      <img class="w-[22px] h-[14px] mr-4" src="../assets/images/mold-base/arrow-right.png" alt="" />
    </div>
    <collapse direction="right" class="absolute z-50 right-0 top-[165px]">
      <div class="viewMainBG w-[826px] h-[908px] rounded-tl-[34px] rounded-bl-[34px]">
        <div class="flex pt-[26px] pl-[10px]">
          <div class="w-1/2">
            <!--测点监控-->
            <div class="ml-3 space-y-3">
              <img class="w-[376px] h-[30px]" src="../assets/images/mold-base/points-monitor.png" alt="测点监控" />

              <el-tabs class="elTabsCus h-[32px]" v-model="sensorTypeId" :before-leave="beforeSensorTypeTabLeave" v-loading="isLoadingSensorTypeList">
                <el-tab-pane v-for="v in sensorTypeList" :key="v.id" :label="v.name" :name="v.id" />
              </el-tabs>

              <div class="w-full min-h-[20px] max-h-[118px] grid grid-cols-7 gap-1 points overflow-auto" v-loading="isLoadingSensorPoints">
                <div
                  v-for="v2 in sensorData?.datas"
                  :key="v2.id"
                  @click="onSensorPointClick(v2)"
                  class="bg-[#25B644] h-[20px] text-center rounded-sm text-sm cursor-pointer"
                  :class="{ error: !v2.online, activated: v2.id === sensorPointId }"
                >
                  {{ v2.name }}
                </div>
              </div>

              <div class="h-[310px]" v-loading="isLoadingSensorPoints || isLoadingPointInfoList">
                <timeline v-if="pointInfoList?.length" :items="pointInfoList">
                  <template #start="{ item }">
                    <div class="text-center font-pingfang-regular">
                      <div class="text-[14px]">
                        {{ item.dayjsTimeObj.format("HH:mm:ss") }}
                      </div>
                      <div class="text-[10px] opacity-40">
                        {{ item.dayjsTimeObj.format("YYYY/MM/DD") }}
                      </div>
                    </div>
                  </template>
                  <template #end="{ item }">
                    <div class="flex justify-between items-center pb-4">
                      <div class="space-y-2">
                        <div class="flex items-center">
                          <div class="mr-2 text-base font-pingfang">
                            {{ sensorTypeSelected?.name }}
                          </div>
                          <div class="text-[12px] font-pingfang-regular opacity-50">
                            {{ sensorPointSelected?.name }}
                          </div>
                        </div>
                        <div class="flex items-center space-x-4">
                          <div>
                            <div class="text-[12px] font-pingfang-regular">监测值</div>
                            <div class="text-[14px] font-pingfang"> {{ item.avalue }}{{ sensorTypeSelected?.unit }} </div>
                          </div>
                          <divide width="1px" height="16px" :opacity="0.3" />
                          <div>
                            <div class="text-[12px] font-pingfang-regular">上限</div>
                            <div class="text-[14px] font-pingfang">
                              {{ sensorTypeSelected?.thresholdmax ? `&lt;=&nbsp;${sensorTypeSelected.thresholdmax}` : "" }}{{ sensorTypeSelected?.unit }}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        class="mr-4 w-[44px] h-[44px] flex justify-center items-center text-[14px] font-pingfang rounded-[6px]"
                        :class="{
                          'bg-[#EF424E]': item.isWarning,
                          'bg-[#5D6A7C]': !item.isWarning,
                        }"
                      >
                        {{ item.isWarning ? "预警" : "安全" }}
                      </div>
                    </div>
                  </template>
                </timeline>
                <div v-else class="text-center">
                  <span v-if="isLoadingSensorPoints || isLoadingPointInfoList"></span>
                  <el-empty v-else description="暂无数据" :image-size="100" />
                </div>
              </div>
            </div>

            <!--视频监控-->
            <div class="ml-3 space-y-2">
              <div class="titleBG mt-2 w-[124px] px-4 py-2">视频监控</div>
              <div class="videoMonitors h-[36px] flex items-center text-sm overflow-hidden" v-loading="isLoadingVideoTabs">
                <div
                  v-for="(item, i) in videoListForView"
                  :key="item.id"
                  :id="item.id"
                  class="w-1/4 flex-none rounded-[2px] text-center py-2 cursor-pointer"
                  :class="{ activated: item.id === videoId }"
                  @click="onVideoMonitorClick(item, i)"
                >
                  {{ item.label }}
                </div>
              </div>
              <div class="w-full h-[6px] bg-[hsla(0,0%,0%,0.2)]">
                <div ref="cursorBar" class="w-[98px] transition-transform duration-300 h-[6px] bg-[#545975]"></div>
              </div>
              <iframe class="w-full h-[224px] rounded-tl-[6px] rounded-tr-[6px]" :src="videoUrl" allowfullscreen frameborder="no" scrolling="no"> </iframe>
              <!-- <video-player
                class="w-full h-[224px]"
                style="border-radius: 8px; object-fit: fill"
                :options="playerOptions"
                @ready="onVideoPlayerReady"
                @dispose="onVideoPlayerDispose"
              /> -->
            </div>
          </div>
          <div class="w-1/2 pl-4 pr-4">
            <!--钢平台状态-->
            <div class="space-y-1">
              <img class="w-[376px] h-[30px]" src="../assets/images/mold-base/steel-platform-status.png" alt="测点监控" />
              <!--钢平台水平度-->
              <div class="pl-4">
                <div class="titleBG w-[172px] px-4 py-2 mt-2">钢平台水平度</div>
                <div id="SPD" class="mt-2 w-full h-[200px]" v-loading="isLoadingSPD"></div>
              </div>
              <!--筒架柱垂直度-->
              <div class="pl-4">
                <div class="flex justify-between">
                  <div class="titleBG w-[172px] px-4 py-2 my-2">筒架柱垂直度</div>
                  <div class="flex items-center space-x-4">
                    <div class="flex items-center text-sm">
                      <div class="w-[12px] h-[12px] mr-4 rounded-half bg-[#3FA446]"></div>
                      整体钢平台
                    </div>
                    <div class="flex items-center text-sm">
                      <div class="w-[12px] h-[12px] mr-4 bg-[#3FA446]"></div>
                      塔吊筒
                    </div>
                  </div>
                </div>
                <div id="SD" class="w-full h-[210px]" v-loading="isLoadingSD"></div>
              </div>
              <!--核心筒应力、钢平台梁应力-->
              <div class="pl-4">
                <div class="flex">
                  <div
                    v-for="item in stressList"
                    :key="item.id"
                    class="stressItem w-[108px] h-[34px] cursor-pointer flex justify-center items-center rounded-[2px] text-sm"
                    :class="{ activated: stressId === item.id }"
                    @click="onStressItemClick(item)"
                  >
                    {{ item.label }}
                  </div>
                </div>
                <div id="YL" class="w-full mt-2 h-[270px]" v-loading="isLoadingYL"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </collapse>

    <el-dialog
      custom-class="dialogCus bg-[#343745] overflow-hidden"
      top="11vh"
      height="900px"
      width="1858px"
      :append-to-body="true"
      @opened="doLegMonitorRequest"
      @close="diposeLegMonitorSource"
      :visible.sync="isLegMonitorDialogVisible"
    >
      <template #title>
        <img class="w-[454px] h-[84px] rounded-tl-[32px]" src="../assets/images/mold-base/title-bg.png" alt="背景" />
      </template>
      <leg-monitor ref="legMonitorRef" />
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  import { MoldBaseAPI } from "../api/moldBase";
  import LegMonitor from "./LegMonitor/index.vue";

  const getSPD = (list) => {
    const hours = ["1", "2", "3", "4"];
    const days = ["1", "2", "3", "4"];
    const records = list;
    const data = [];
    let i = 0;
    // eslint-disable-next-line no-unused-vars
    ["4", "3", "2", "1"].map((hours, index) => {
      days.map((days, itemIndex) => {
        data.push([itemIndex, hours, i, records[i].monitor_value]);
        i++;
      });
    });
    return {
      grid: {
        left: 0,
        right: 0,
        bottom: 60,
        top: 0,
        containLabel: true,
      },
      tooltip: {
        position: "top",
        formatter: (row) => {
          const item = records[row.data[2]];
          return "名称：" + item.monitor_point_name + "</br>" + "值：" + item.monitor_value + "mm";
        },
      },
      xAxis: {
        type: "category",
        data: hours,
        splitArea: {
          show: true,
          areaStyle: {
            color: ["rgba(255,255,255,0.025)", "rgba(255,255,255,0.05)"],
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: false,
          color: "#fff",
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: "category",
        data: days,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ["rgba(255,255,255,0.025)", "rgba(255,255,255,0.05)"],
          },
        },
        axisLabel: {
          show: false,
          color: "#fff",
        },
      },
      visualMap: {
        min: -20,
        max: 20,
        calculable: true,
        orient: "horizontal",
        left: "center",
        bottom: 0,
        textStyle: {
          color: "#fff",
        },
        inRange: {
          color: ["#EAC492", "#DB1E1E"],
        },
        formatter: function (value) {
          return value + "mm";
        },
      },
      series: [
        {
          type: "heatmap",
          data: data,
          label: {
            show: true,
            rich: {
              b: {
                lineHeight: 14,
                align: "center",
                color: "#fff",
              },
              a: {
                lineHeight: 14,
                align: "center",
                color: "#fff",
              },
            },
            formatter: (row) => {
              const item = records[row.data[2]];
              const list = ["{a|" + item.monitor_point_code + "}", "{b|" + item.monitor_value + "mm}"].join("\n");
              return list;
            },
          },
          itemStyle: {
            borderWidth: 2,
            borderType: "solid",
            borderColor: "#1d284e",
          },
          encode: {
            tooltip: [3],
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };
  };

  const getSD = (list1, list2) => {
    return {
      grid: {
        left: "3%",
        right: "7%",
        bottom: "15%",
        top: "8%",
        containLabel: true,
      },
      tooltip: {
        // trigger: 'axis',
        showDelay: 0,
        formatter: function (params) {
          return `${params.value[5]}:${params.value[0]},${params.value[6]}:${params.value[1]}`;
        },
        axisPointer: {
          show: true,
          type: "cross",
          lineStyle: {
            type: "dashed",
            width: 1,
          },
        },
      },
      toolbox: {
        show: false,
        feature: {
          dataZoom: {},
          brush: {
            type: ["clear"],
          },
        },
        top: -10,
        right: 25,
      },
      brush: {},
      xAxis: [
        {
          type: "value",
          scale: true,
          max: 10,
          min: -10,
          minInterval: 5,
          axisLabel: {
            color: "rgba(255,255,255,0.8)",
          },
          // splitLine: {
          //     show: true
          // }
          axisLine: {
            show: true,
            lineStyle: {
              color: "#1C517B",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(28,81,123,0.55)",
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          scale: true,
          // axisLabel: {
          //     formatter: '{value} kg'
          // },
          max: 10,
          min: -10,
          minInterval: 5,
          axisLabel: {
            color: "rgba(255,255,255,0.8)",
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#1C517B",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(28,81,123,0.55)",
            },
          },
          data: [],
        },
      ],
      series: [
        {
          name: "",
          type: "scatter",
          emphasis: {
            focus: "series",
          },
          color: "#3FA446",
          symbolSize: 12,
          symbol: function (data) {
            return data[2];
          },
          data: list1,
          // [
          //     // 圆形
          //     [3, 3, 'circle'],
          //     // 矩形
          //     [4, 4, 'rect'],
          //     // 三角形
          //     // [2, 2, 'triangle'],

          // ],

          markArea: {
            silent: true,
            itemStyle: {
              color: "transparent",
              borderWidth: 1,
              borderColor: "#00A0E9",
              borderType: "dashed",
            },
            data: [
              [
                {
                  name: "",
                  xAxis: -5,
                  yAxis: -5,
                },
                {
                  xAxis: 5,
                  yAxis: 5,
                },
              ],
            ],
          },
          // markPoint: {
          //     data: [
          //         { type: 'max', name: 'Max' },
          //         { type: 'min', name: 'Min' }
          //     ]
          // },
          // markLine: {
          //     lineStyle: {
          //         type: 'solid'
          //     },
          //     data: [{ type: 'average', name: 'AVG' }, { xAxis: 160 }]
          // }
        },
        {
          name: "Male2",
          type: "scatter",
          emphasis: {
            focus: "series",
          },
          color: "#FF4946",
          symbolSize: 12,
          symbol: function (data) {
            return data[2];
          },
          data: list2,
          // [

          //     [8, 6, 'circle'],
          //     [9, -6, 'rect'],
          //     // [5, 9, 'triangle'],

          // ],
          markArea: {
            silent: true,
            itemStyle: {
              color: "transparent",
              borderWidth: 1,
              borderColor: "#F55A47",
              borderType: "dashed",
            },
            data: [
              [
                {
                  name: "",
                  xAxis: -7.5,
                  yAxis: -7.5,
                },
                {
                  xAxis: 7.5,
                  yAxis: 7.5,
                },
              ],
            ],
          },
          // markPoint: {
          //     data: [
          //         { type: 'max', name: 'Max' },
          //         { type: 'min', name: 'Min' }
          //     ]
          // },
          // markLine: {
          //     lineStyle: {
          //         type: 'solid'
          //     },
          //     data: [{ type: 'average', name: 'Average' }, { xAxis: 170 }]
          // }
        },
      ],
    };
  };

  export default {
    name: "MoldBase",
    components: {
      "leg-monitor": LegMonitor,
    },
    data() {
      return {
        isLegMonitorDialogVisible: false, //油缸/牛腿监控弹窗是否可见
        sensorTypeList: [], //传感器类型列表
        sensorTypeId: "", //选中的传感器类型Id
        isLoadingSensorTypeList: false, //传感器类型列表加载标志

        sensorData: null, //选中传感器类型的数据（.datas测点列表）
        sensorPointId: "", //选中的测点Id
        isLoadingSensorPoints: false, //传感器类型下测点列表加载标志

        pointInfoList: [], //选中测点的时间线数据列表
        isLoadingPointInfoList: false, //选中测点时间线数据加载标志

        isLoadingVideoTabs: false, //加载视频标志
        videoTabs: [],
        videoId: "",
        videoUrl: "",
        videoIndex: null,
        stressList: [
          {
            id: 3,
            label: "核心筒梁应力",
          },
          {
            id: 1,
            label: "钢平台梁应力",
          },
        ],
        playerOptions: { liveui: true },
        videoPlayerInstance: null,

        stressId: 3, // 核心筒梁应力3， 钢平台梁应力 1

        echartInstances: [],
        isLoadingSPD: false, //加载钢平台水平度标志
        isLoadingSD: false, //加载筒架柱垂直度
        isLoadingYL: false, //应力加载标志
      };
    },
    computed: {
      videoListForView() {
        return this.videoTabs?.slice(0, 4) ?? [];
      },
      sensorTypeSelected() {
        return this.sensorTypeList.find((item) => item.id === this.sensorTypeId);
      },
      sensorPointSelected() {
        return this.sensorData?.datas?.find((item) => item.id === this.sensorPointId);
      },
      ...mapGetters("common", ["currentProjectId"]),
    },
    watch: {
      currentProjectId: {
        immediate: true,
        handler(cv) {
          cv && this.updateThePage();
        },
      },
    },
    methods: {
      //更新页面
      updateThePage() {
        this.dispose();
        this.updateSensorTypeList();
        this.updateVideoTabs().then(() => {
          if (this.videoListForView.length) {
            this.updateVideoPlaySrc(this.videoListForView[0]);
          }
        });
        this.drawSPD();
        this.drawSD();
        this.getYLByTypeId();
      },

      //释放
      dispose() {
        this.echartInstances.forEach((item) => {
          item?.dispose();
        });
        this.echartInstances = [];
      },

      //更新传感器类别列表
      async updateSensorTypeList() {
        this.isLoadingSensorTypeList = true;
        //id 1模架 2环境
        this.sensorTypeList = [];
        const res = await MoldBaseAPI.getSensorTypeList({ belong: 1 });
        this.sensorTypeList = res?.code === 0 ? res.data : [];
        this.isLoadingSensorTypeList = false;
        this.sensorTypeId = this.sensorTypeList[0]?.id ?? "";
        this.$nextTick(() => {
          this.updateSensorTypePoints(this.sensorTypeId);
        });
      },

      //更新选中的传感器类型测点列表
      async updateSensorTypePoints(sensorTypeId) {
        this.sensorData = null;
        if (sensorTypeId) {
          this.isLoadingSensorPoints = true;
          const res = await MoldBaseAPI.getSensorData({ categoryId: sensorTypeId });
          this.sensorData = res?.code === 0 ? res.data : null;
          this.isLoadingSensorPoints = false;
        }
        this.onSensorPointClick(this.sensorData?.datas?.[0]);
      },

      //响应测点点击事件
      async onSensorPointClick(item) {
        if (!item || item.id === this.sensorPointId || this.isLoadingPointInfoList) return;
        this.sensorPointId = item.id;
        this.isLoadingPointInfoList = true;
        this.pointInfoList = [];
        const res = await MoldBaseAPI.getPointInfoList({ pointid: item.id });
        this.pointInfoList = (res?.code === 0 ? res.data : []).map((item) => {
          return {
            ...item,
            dayjsTimeObj: this.$dayjs(item.surveydate),
            isWarning: item.isabnormal !== 0,
          };
        });
        this.isLoadingPointInfoList = false;
      },

      //传感器类型tab页签激活前（该函数会在页面加载后自动执行一次）
      beforeSensorTypeTabLeave(cName, oName) {
        // console.log("beforeSensorTypeTabLeave", cName, oName, this.sensorTypeId);
        if (this.isLoadingPointInfoList || this.isLoadingSensorPoints) return false; //加载测点列表、加载测点信息列表状态时，不允许激活其他tab
        else if (!oName) {
          return true; //首次加载
        } else if (cName && cName !== oName) {
          this.updateSensorTypePoints(cName);
        }
      },

      //更新视频监控列表
      async updateVideoTabs() {
        this.isLoadingVideoTabs = true;
        this.videoTabs = [];
        // const res = await MoldBaseAPI.getVideoList({ belong: 1 });
        const res = await MoldBaseAPI.getVideoSourceList();
        this.videoTabs = (res?.code === 0 ? res.data : [])
          .filter((item) => item.name === "摄像头")?.[0]
          ?.children?.map((item) => {
            return {
              ...item,
              id: item.id + "",
              label: item.name,
            };
          }); //从结果集当中取出摄像头的数据;
        this.isLoadingVideoTabs = false;
        const first = this.videoTabs[0];
        this.videoId = first?.id;
        if (first?.url) {
          this.videoUrl = `https://open.ys7.com/ezopen/h5/iframe?url=${first.url}`;
        } else {
          this.videoUrl = "";
        }
        this.$nextTick(this.initCursorBarPosition);
      },

      //初始化视频滚动条
      initCursorBarPosition() {
        let index = (this.videoIndex = this.videoListForView.findIndex((item) => item.id === this.videoId));
        if (index !== -1) {
          this.animateCursorBar(this.videoListForView[index].id, index);
        }
      },

      //使视频滚动条产生动画
      animateCursorBar(id, i) {
        const current = document.getElementById(id);
        if (current) {
          const info = current.getBoundingClientRect();
          this.$refs.cursorBar.style = `width:${info.width}px;transform:translate3d(${i * info.width}px,0,0)`;
        }
      },

      //响应点击视频tab
      onVideoMonitorClick(item, i) {
        if (item.id === this.videoId) return;
        this.videoId = item.id;
        this.videoUrl = `https://open.ys7.com/ezopen/h5/iframe?url=${item.url}`;
        this.animateCursorBar(item.id, i);
        this.videoIndex = i;
        // this.updateVideoPlaySrc(item);
      },

      //更新VideoPlayer的视频源
      updateVideoPlaySrc(item) {
        // item.url = "http://kbs-dokdo.gscdn.com/dokdo_300/_definst_/dokdo_300.stream/playlist.m3u8"; //mock url
        this.videoPlayerInstance?.src({ type: "application/x-mpegURL", src: item.url });
      },

      //响应VideoPlayer的ready事件
      onVideoPlayerReady(player) {
        this.videoPlayerInstance = player;
        player.on("error", function () {
          this.loadingSpinner.hide();
        });
      },

      //响应VideoPlayer的dispose事件
      onVideoPlayerDispose() {
        this.videoPlayerInstance?.dispose();
        this.videoPlayerInstance = null;
      },

      //牛腿监控弹窗打开，调用业务函数
      doLegMonitorRequest() {
        if (this.$refs?.legMonitorRef) {
          this.$refs.legMonitorRef?.doBusiness();
        }
      },

      //牛腿弹窗关闭，释放
      diposeLegMonitorSource() {
        this.$refs.legMonitorRef?.dispose();
      },

      //绘制钢平台水平度
      async drawSPD() {
        this.isLoadingSPD = true;
        const res = await MoldBaseAPI.getHorizontalUp();
        this.isLoadingSPD = false;
        if (res?.code === 0) {
          const chartDom = document.getElementById("SPD");
          if (chartDom) {
            const myChart = this.$echarts.init(chartDom);
            const option = getSPD(res.data.datas);
            myChart.setOption(option);
            this.echartInstances.push(myChart);
          }
        }
      },

      //筒架力垂直度
      async drawSD() {
        this.isLoadingSD = true;
        const res = await MoldBaseAPI.getVerticalPosition();
        this.isLoadingSD = false;
        if (res?.code === 0) {
          const { mold_base, tower_crane } = res.data;
          let x = 0;
          let y = 0;
          let xName = "";
          let yName = "";
          if (mold_base.datas.length > 0) {
            x = mold_base.datas[0].monitor_point_id_x;
            y = mold_base.datas[0].monitor_point_id_y;
            xName = mold_base.datas[0].monitor_point_code_x;
            yName = mold_base.datas[0].monitor_point_code_y;
          } else if (tower_crane.datas.length > 0 && x === 0 && y === 0) {
            x = tower_crane.datas[0].monitor_point_id_x;
            y = tower_crane.datas[0].monitor_point_id_y;
            // eslint-disable-next-line no-unused-vars
            xName = tower_crane.datas[0].monitor_point_code_x;
            // eslint-disable-next-line no-unused-vars
            yName = tower_crane.datas[0].monitor_point_code_y;
          }
          // 筒架柱水平度
          // _this.SDUrl = mold_base.plan_drawings_url2;
          // _this.SDList = [mold_base.plan_drawings_url2, tower_crane.plan_drawings_url2];
          const list1 = [];
          const list2 = [];
          // 此处要判断是否超过阈值，超过进list2
          const xMax = 7.5;
          const yMax = 7.5;
          mold_base.datas.map((item) => {
            if (Math.abs(item.x) < xMax && Math.abs(item.y) < yMax) {
              list1.push([item.x, item.y, "rect", item.monitor_point_id_x, item.monitor_point_id_y, item.monitor_point_code_x, item.monitor_point_code_y]);
            } else {
              list2.push([item.x, item.y, "rect", item.monitor_point_id_x, item.monitor_point_id_y, item.monitor_point_code_x, item.monitor_point_code_y]);
            }
          });
          tower_crane.datas.map((item) => {
            if (Math.abs(item.x) < xMax && Math.abs(item.y) < yMax) {
              list1.push([item.x, item.y, "circle", item.monitor_point_id_x, item.monitor_point_id_y, item.monitor_point_code_x, item.monitor_point_code_y]);
            } else {
              list2.push([item.x, item.y, "circle", item.monitor_point_id_x, item.monitor_point_id_y, item.monitor_point_code_x, item.monitor_point_code_y]);
            }
          });
          const chartDom = document.getElementById("SD");
          if (chartDom) {
            const myChart = this.$echarts.init(chartDom);
            const option = getSD(list1, list2);
            myChart.setOption(option);
            this.echartInstances.push(myChart);
          }
        }
      },

      //核心筒梁应力3、钢平台梁应力1
      async getYLByTypeId() {
        this.isLoadingYL = true;
        const res = await MoldBaseAPI.getPointsListLine({ typeId: this.stressId });
        this.isLoadingYL = false;
        if (res?.code === 0) {
          const chartDom = document.getElementById("YL");
          if (chartDom) {
            const chartInstance = this.$echarts.getInstanceByDom(chartDom);
            chartInstance?.dispose();
            const myChart = this.$echarts.init(chartDom);
            const option = this.getYL({}, res.data.datas, res.data.summary.monitor_item_unit);
            myChart.setOption(option);
            this.echartInstances.push(myChart);
          }
        }
      },

      //应力
      getYL(options = {}, data, yName) {
        const nameList = data.map((item) => {
          return item.monitor_point_code;
        });
        const dataList = data.map((item) => {
          return item.monitor_value;
        });
        return {
          color: [
            new this.$echarts.graphic.LinearGradient(
              // 4个参数用于配置渐变色的起止位置, 这4个参数依次对应右/下/左/上四个方位. 而0 0 0 1则代表渐变色从正上方开始
              0,
              0,
              1,
              0,
              [
                { offset: 0, color: "#F3A74A" },
                { offset: 1, color: "#EDCD6D" },
              ],
            ),
          ],
          grid: {
            top: 40,
            left: 40,
            right: 12,
            bottom: 36,
          },
          tooltip: {
            // axisPointer:{
            //   lineStyle:false
            // }
          },
          yAxis: {
            type: "value",
            name: yName,
            nameLocation: "end",
            nameGap: 16,
            nameTextStyle: {
              align: "right",
              color: "#B0D0FF",
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#eee",
                opacity: 0.2,
              },
            },
            min: -200,
            max: 200,
            offset: 5,
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#B0D0FF",
              },
            },
            axisLabel: {
              color: "#B0D0FF",
              showMaxLabel: true,
            },
          },
          xAxis: {
            type: "category",
            data: nameList,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#B0D0FF",
            },
          },
          series: [
            {
              data: dataList,
              type: "bar",
              barWidth: 8,

              itemStyle: {
                borderRadius: [20, 20, 20, 20],
              },
            },
          ],

          ...options,
        };
      },

      //响应应力tab点击事件
      onStressItemClick(item) {
        if (item.id === this.stressId) return;
        this.stressId = item.id;
        this.$nextTick(this.getYLByTypeId);
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::v-deep .dialogCus {
    border-radius: 32px;
    margin-bottom: 0 !important;

    .el-dialog {
      position: relative;
    }

    .el-dialog__header {
      padding: 0;

      .el-dialog__close {
        font-size: 22px;
      }
    }

    .el-dialog__body {
      color: #fff;
      padding: 0;
      height: 800px;
    }
  }

  .legMonitorShadow {
    box-shadow: -1px 0px 25px 0px rgba(14, 15, 15, 0.1);
  }

  .viewMainBG {
    background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9) 0%, hsla(236, 33%, 18%, 0.9) 100%) no-repeat left center / cover border-box;
  }

  .titleBG {
    background: url("/images/title-bg.png") no-repeat left center / cover border-box;
  }

  .points {
    .error {
      background-color: hsla(356, 84%, 60%, 1);
    }
    .activated {
      background-color: #1e5fd5;
    }
  }

  .videoMonitors {
    .activated {
      background-color: #1e5fd5;
    }
  }

  .stressItem.activated {
    background-color: #1e5fd5;
  }

  ::v-deep .el-tabs.elTabsCus {
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      height: 32px;
      line-height: 32px;
      &.is-disabled {
        cursor: not-allowed;
      }
    }
    .el-tabs__nav-scroll {
      .el-tabs__item {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        text-align: center;
        color: #fff;

        &.is-active {
          background-color: #1e5fd5;
        }
        &:nth-child(2) {
          padding-left: 20px;
        }
        &:last-child {
          padding-right: 20px;
        }
      }
    }
    .el-tabs__content {
      display: none;
    }
    .el-tabs__active-bar {
      display: none;
    }
  }
</style>
