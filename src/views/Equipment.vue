<template>
  <div class="viewWrapper">
    <!--右侧内容区,右侧-->
    <collapse direction="right" class="absolute z-50 right-0 top-32">
      <div class="viewMainBG flex justify-between items-center w-[806px] h-[900px] pt-6 rounded-tl-[34px] rounded-bl-[34px] roundedMaskFlag">
        <div class="flex-none w-1/2 pl-1 h-full">
          <!--塔吊-->
          <div class="pl-4">
            <!--背景-->
            <img class="w-[376px] h-[30px]" src="../assets/images/equipment/tower-crane.png" alt="塔吊" />
            <!--塔吊图表-->
            <div class="flex h-[144px] justify-between mt-6 font-pingfang-regular" v-loading="isLoadingTowerCraneList">
              <div class="w-1/2" v-for="(item, index) in towerCraneList" :key="index">
                <div class="flex items-center">
                  <div class="flex-1 text-center space-y-3">
                    <div class="text-[14px]">安全分</div>
                    <div :id="'TowerChart' + item.id" class="w-full h-[74px]"></div>
                    <div class="flex justify-center items-center">
                      <div :style="{ backgroundColor: item.location == '在线' ? '#43EF68' : '#fff' }" class="w-3 h-3 mr-1 rounded-half"></div>
                      <div class="text-base flex items-center">
                        <el-badge v-if="item.alarm_count > 0" :value="item.alarm_count">
                          <span class="text-[#FFF] text-opacity-60">{{ item.name }}</span>
                        </el-badge>
                        <span v-else class="text-[#FFF] text-opacity-60">{{ item.name }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex-none w-[50px] text-[14px] cursor-pointer" @click="openTowerCraneDetail(item)">
                    <span class="mr-2">详情</span><i class="el-icon-arrow-right"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--升降机-->
          <div class="pl-4 mt-4">
            <!--背景-->
            <img class="w-[376px] h-[30px]" src="../assets/images/equipment/elevator.png" alt="升降机" />
            <div class="mt-4 pl-4" v-loading="isLoadingElevatorList">
              <div class="flex items-center space-x-5 h-[40px]">
                <div
                  v-for="item in elevatorList"
                  :class="{ activated: elevatorActivatedId === item.id }"
                  class="elevatorItem rounded-md py-2 px-4 cursor-pointer"
                  :key="item.id"
                  @click="onClickElevator(item)"
                  >{{ item.name }}</div
                >
              </div>
              <div class="mt-4 h-[288px]" :key="elevatorActivatedId" v-loading="!isLoadingElevatorList && isLoadingElevatorDetailInfo">
                <div class="flex space-x-5">
                  <div class="elevatorBG flex-1 flex w-[162px] h-[68px] px-3 pt-3 pb-8 justify-between">
                    <div class="flex items-center">
                      <img src="/images/env/level.png" class="w-[16px] h-[16px] mr-3" alt="" />
                      楼层
                    </div>
                    <div>{{ pageElevatorDetailInfo?.sensors?.find((item) => item.name === "楼层")?.val ?? "" }}</div>
                  </div>
                  <div class="elevatorBG flex-1 flex w-[162px] h-[68px] px-3 pt-3 pb-8 justify-between">
                    <div class="flex items-center">
                      <img src="../assets/images/equipment/angle.png" class="w-[16px] h-[16px] mr-3" alt="" />
                      倾角
                    </div>
                    <div>{{ pageElevatorDetailInfo?.sensors?.find((item) => item.name === "倾角")?.val ?? "" }}</div>
                  </div>
                  <div class="flex-none flex items-center -mt-[18px] text-[14px] cursor-pointer" @click="openElevatorDetail(elevatorActivated)">
                    <div class="mr-2">更多</div>
                    <i class="el-icon-arrow-right"></i>
                  </div>
                </div>
                <div class="space-y-3">
                  <div
                    v-for="item in pageElevatorDetailInfo?.sensors?.filter((item) => item.name.includes('应力'))"
                    :key="item.id"
                    class="flex items-center space-x-3"
                  >
                    <div class="w-[72px] text-[14px] leading-[14px]">{{ item.name }}</div>
                    <el-progress class="flex-auto" :percentage="Number(item.val)" :format="formatYingLi" :color="customColor" :stroke-width="customWidth" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--围挡-->
          <div class="mt-4 pl-4">
            <!--背景-->
            <img class="w-[376px] h-[30px]" src="../assets/images/equipment/enclosure.png" alt="围挡" />
            <div class="relative flex space-x-3 pl-4 mt-4">
              <div class="flex space-x-2 items-center text-[14px]">
                <div class="w-6 h-2 bg-[#EF424E]"></div>
                <div>离线</div>
              </div>
              <div class="flex space-x-2 items-center text-[14px]">
                <div class="w-6 h-2 bg-[#25B644]"></div>
                <div>在线</div>
              </div>
              <div class="absolute right-0 flex space-x-2 items-center cursor-pointer text-[14px]" @click="enclosureAlarmRecord">
                <div> 报警记录 </div>
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
            <div class="inline-block mt-[6px] pt-[4px] pl-[12px] w-full h-[160px] overflow-auto" v-loading="isLoadingEnclosureList">
              <div
                v-for="(item, index) in enclosureList"
                :key="index"
                class="inline-block px-[10px] py-[3px] mr-[4px] mb-[4px] text-center bg-[#25B644] rounded-sm"
                :class="{ offline: item.location === '离线' }"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>

        <div class="flex-none w-1/2 pl-4 h-full">
          <!--设备报警-->
          <div class="mb-[24px] h-[485px]">
            <!--背景-->
            <img class="w-[376px] h-[30px]" src="../assets/images/equipment/alarm.png" alt="设备报警" />
            <!--累计、未处置-->
            <div class="mt-3 mx-4 h-[220px]" v-loading="isLoadingEquipmentWarningTotal">
              <div class="flex space-x-4">
                <div class="alarmBG mt-3 w-[162px] h-[68px] px-3 pt-3 pb-8 cursor-pointer" @click="onDeviceWarningClick(-1)">
                  <div>累计报警</div>
                  <div>
                    <span class="text-[24px] font-ding mr-3">{{ equipmentAlarmTotal?.total }}</span>
                    <span class="opacity-60">次</span>
                  </div>
                </div>
                <div class="alarmBG mt-3 w-[162px] h-[68px] px-3 pt-3 pb-8 cursor-pointer" @click="onDeviceWarningClick(0)">
                  <div>未处置报警</div>
                  <div>
                    <span class="text-[24px] font-ding mr-3">{{ equipmentAlarmTotal?.un_handler }}</span>
                    <span class="opacity-60">次</span>
                  </div>
                </div>
              </div>
              <div class="mt-4 space-y-3">
                <div v-for="(v, i) in equipmentAlarmTotal?.datas" :key="i" class="flex items-center pl-3">
                  <div class="flex-none w-[90px] text-[14px]">{{ v.name }}</div>
                  <div v-if="v.count" style="max-width: calc(100% - 100px - 40px)" class="alarmBar pl-[6px] flex space-x-[2px] items-center overflow-x-hidden">
                    <div v-for="k in Math.floor(v.count * zoomFactor)" :key="k" class="flex-none w-[2px] h-[10px] bg-[#E65851]"></div>
                  </div>
                  <div class="flex-none w-[40px] pl-[6px] text-[14px]">{{ v.count }}</div>
                </div>
              </div>
            </div>
            <!--报警时间线-->
            <div class="mx-4 h-[210px]" v-loading="isLoadingEquipmentWarningList">
              <div class="mt-6 pt-[12px]">
                <el-timeline class="ml-[20px] mr-[16px] eltimelineCus">
                  <el-timeline-item v-for="(v, i) in equipmentAlarmListForView" :key="i">
                    <div class="flex text-[#fff] -ml-[12px] pl-2">
                      <div class="w-1/2 -mt-[12px] space-y-2">
                        <div class="flex text-base">
                          <div>{{ v.type_name }}</div>
                        </div>
                        <div class="flex text-[14px]">
                          <div>{{ v.equipment_name }}</div>
                        </div>
                      </div>
                      <div class="w-1/2 -mt-[12px] space-y-2 text-right">
                        <div>{{ $dayjs(v.risk_time).fromNow() }}</div>
                        <div v-if="v.status === '已处置'" class="text-[#2CE7B2]">{{ v.status }}</div>
                        <div v-else class="text-[#EF424E]">{{ v.status }}</div>
                      </div>
                    </div>
                    <template #dot>
                      <div class="withGradientBG"></div>
                    </template>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
          </div>

          <!--设备类型-->
          <div class="pl-4 h-[335px]">
            <!--背景-->
            <img class="w-[376px] h-[30px]" src="../assets/images/equipment/type.png" alt="设备类型" />
            <div class="mr-4 mt-3" v-loading="isLoadingHereEquipmentInfo">
              <div class="hereNumsBG inline-block w-[118px] h-[82px] mt-4 ml-4 pt-3 pl-4">
                <div>在场设备</div>
                <div class="font-ding text-[32px]">{{ availableEquipmentInfo?.total }}</div>
              </div>
              <div id="hereEquChart" class="-mt-[14px] w-[320px] min-h-[164px]"></div>
              <div class="flex">
                <div v-for="(v, i) in availableEquipmentInfo.details" :key="i" class="flex-1 flex items-center justify-around">
                  <div class="flex">
                    <div class="w-[12px] h-[12px] border-[2px] rounded-half mt-2 mr-3" :style="{ border: `1px solid ${equipmentsColorMap[v.name]}` }"></div>
                    <div>
                      <div class="font-ding text-[24px]">{{ v.count }}</div>
                      <div class="text-[14px]">{{ v.name }}</div>
                    </div>
                  </div>
                  <divide v-if="i !== availableEquipmentInfo.details.length - 1" width="1px" height="32px" :opacity="0.3" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </collapse>
    <!-- 围挡报警记录 -->
    <dialog-fixed
      :visible.sync="enclosureRecordVisible"
      width="949px"
      height="464px"
      closeIconTop="14px"
      @open="onEnclosureArarmListOpen"
      @close="resetEnclosureAlarmList"
    >
      <div class="flex items-center justify-center w-full h-[36px] mt-[31px]">
        <img src="../assets/images/equipment/enclosure-alarm-info.png" class="w-[575px]" alt="" />
      </div>
      <div style="height: calc((100% - 106px) - 40px)" class="mt-[37px] ml-[49px] mr-[43px]">
        <el-table :data="enclosureAlarmList" size="medium" height="calc(100% - 58px)" style="width: 100%">
          <el-table-column v-for="item in listTitle" :key="item.label" :min-width="item.width" :label="item.label" :prop="item.property" show-overflow-tooltip>
            <template slot-scope="scope">
              <div v-if="item.property == 'alarm'" style="color: #ff4a34">{{ scope.row[item.property] }}</div>
              <div v-else>{{ scope.row[item.property] }}</div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background layout="prev, pager, next" :total="10" class="flex justify-end items-center mt-[20px]" />
      </div>
    </dialog-fixed>
    <!-- 塔吊信息   -->
    <dialog-fixed :visible.sync="towerCraneVisible" width="1180px" height="784px" closeIconTop="24px" closeIconRight="10px" @close="resetTowerCraneInfo">
      <div class="flex items-center justify-center w-full h-[36px] mt-[32px]">
        <img src="../assets/images/equipment/tower-crane-info.png" class="w-[575px]" alt="" />
      </div>
      <div style="height: calc(100% - 99px - 52px)" class="mt-[27px] ml-[44px] mr-[36px]" v-loading="isLoadingTower">
        <div class="h-[412px]">
          <div class="flex justify-between h-[89px] font-pingfang-regular">
            <div class="w-[500px] mt-[8px]">
              <div class="flex items-center">
                <div
                  style="line-height: 25px"
                  class="w-[47px] h-[25px] text-center text-[#FFF] rounded-sm"
                  :class="{
                    'bg-[#368F1E]': towerCraneDetail?.location === '在线',
                    'bg-[#596068]': towerCraneDetail?.location === '离线',
                  }"
                >
                  {{ towerCraneDetail?.location ?? "" }}
                </div>
                <div class="text-[21px] text-[#FFF] ml-[8px]">{{ towerCraneDetail?.name }} </div>
                <div class="text-[14px] text-[#FFF] ml-[22px] mt-[7px]">{{ towerCraneDetail?.user_name + " " + towerCraneDetail?.user_tel }}</div>
              </div>
              <div class="text-[14px] text-[hsla(0,0%,100%,0.5)] mt-[12px]">{{
                `保养周期：${towerCraneDetail?.service_interval ?? ""}天 | 下次保养时间：${towerCraneDetail?.service_date ?? ""}（剩余${
                  towerCraneDetail?.service_next ?? ""
                }天）`
              }}</div>
            </div>
            <div class="flex w-[416px] text-[16px] text-[#FFF]">
              <div class="mt-[34px]">安全分</div>
              <div id="towerDetailScoreChart" class="w-[80px] h-[80px] ml-[24px]"></div>
              <div class="w-[1px] h-[35px] mt-[27px] ml-[36px] bg-[hsla(0,0%,100%,0.5)]"></div>
              <img src="../assets/images/equipment/tower-icon.png" class="w-[51px] ml-[42px]" alt="" />
              <div class="flex cursor-pointer" @click="towerGroupMonitorVisible = true">
                <div class="ml-[27px] mt-[34px]">群塔监控</div>
                <img src="../assets/images/equipment/towards-right.png" class="w-[11px] h-[12px] ml-[20px] mt-[39px]" alt="" />
              </div>
            </div>
          </div>
          <!--工作参数、结构参数-->
          <div class="flex h-[323px]">
            <div class="w-[270px] h-full">
              <div class="flex h-[34px]">
                <div style="line-height: 34px" class="w-[108px] h-[34px] text-center bg-[#1E5FD5] rounded text-[#FFF] text-[15px]">工作参数</div>
                <!-- <div style="line-height: 34px" class="w-[108px] h-[34px] text-center rounded text-[#FFF] text-[15px]">结构参数</div> -->
              </div>
              <div class="relative mt-[14px]">
                <div class="bg-[#252F38] w-[110px] h-[253px] rounded-md"></div>
                <div class="absolute top-0 left-0 h-[253px] w-full overflow-auto">
                  <div
                    v-for="(item, index) in tcWorkingParameters"
                    class="workParameters"
                    :class="{ activated: item.code === activatedParamCode }"
                    :key="index"
                  >
                    <div class="w-[130px] text-[#FFF] pl-[14px]">{{ item.name }}</div>
                    <div class="w-[90px] text-[#FFF]">{{ item.val + item.unit }}</div>
                    <div class="examine" @click="examineList(item, 'tcWorkingParameterChart')">查看</div>
                  </div>
                </div>
              </div>
            </div>
            <div style="width: calc(100% - 270px)" class="h-full">
              <div id="tcWorkingParameterChart" class="ml-[30px] h-full"></div>
            </div>
          </div>
        </div>
        <div style="height: calc(100% - 412px)" class="flex">
          <div class="w-1/2 h-full pl-[20px]">
            <div class="w-[326] h-[27px]">
              <img src="../assets/images/equipment/alarm-information.png" alt="" class="h-full" />
            </div>
            <div style="height: calc(100% - 27px - 20px)" class="mt-[20px] overflow-auto">
              <template v-if="towerCraneAlarmArr.length < 1">
                <div class="flex justify-center items-center w-full h-full">
                  <div class="w-[50px] h-[82px]">
                    <img src="../assets/images/equipment/no-record.png" class="w-[41px] h-[53px]" alt="" />
                    <div class="text-[hsla(0,0%,100%,0.5)] text-[12px] mt-[14px]">暂无数据</div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div v-for="(item, index) in towerCraneAlarmArr" class="flex items-center h-[55px]" :key="index">
                  <div class="w-[73px] h-[30px]">
                    <div class="text-[14px] text-[#FFF] text-center">{{ $dayjs(item.time).format("HH:mm:ss") }}</div>
                    <div class="text-[10px] text-[hsla(0,0%,100%,0.4)] text-center">{{ $dayjs(item.time).format("YYYY/MM/DD") }}</div>
                  </div>
                  <div class="relative w-[1px] h-full bg-[#FFF] ml-[24px]">
                    <div class="withGradientMiddleBG"></div>
                  </div>
                  <div class="ml-[29px] w-[262px] text-[14px] text-[#FFF] font-semibold">{{ item.content }}</div>
                  <div class="ml-[4px]">
                    <span class="inline-block bg-[#FF4A34] w-[7px] h-[7px] rounded-half"></span>
                    <span class="ml-[8px] text-[#FF4A34]">预警</span>
                  </div>
                  <div
                    :style="{ 'background-color': item.status == 1 ? '#EF424E' : '#5D6A7C' }"
                    style="line-height: 39px"
                    class="ml-[26px] w-[51px] h-[39px] rounded-[6px] text-xs text-[#FFF] text-center font-semibold"
                    >{{ item.status == 1 ? "未处置" : "已处置" }}</div
                  >
                </div>
              </template>
            </div>
          </div>
          <div class="w-1/2 h-full">
            <div class="h-[27px]">
              <img src="../assets/images/equipment/risk-information.png" alt="" class="h-full" />
            </div>
            <div style="height: calc(100% - 27px - 26px)" class="mt-[26px] ml-[31px]">
              <div v-if="towerCraneResk.length < 1" class="flex justify-center items-center w-full h-full">
                <div class="w-[50px] h-[82px]">
                  <img src="../assets/images/equipment/no-record.png" class="w-[41px] h-[53px]" alt="" />
                  <div class="text-[hsla(0,0%,100%,0.5)] text-[12px] mt-[14px]">暂无数据</div>
                </div>
              </div>
              <el-table v-else :data="towerCraneResk" size="medium" height="100%" style="width: 100%">
                <el-table-column
                  v-for="item in towerCraneReskTitle"
                  :key="item.label"
                  :min-width="item.width"
                  :label="item.label"
                  :prop="item.property"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <div v-if="item.property == 'level'">
                      <div v-if="scope.row[item.property] == '1'">
                        <span class="inline-block bg-[#1988E8] w-[7px] h-[7px] rounded-half"></span>
                        <span class="ml-[8px] text-[#1988E8]">提示</span>
                      </div>
                      <div v-else-if="scope.row[item.property] == '2'">
                        <span class="inline-block bg-[#FFA42E] w-[7px] h-[7px] rounded-half"></span>
                        <span class="ml-[8px] text-[#FFA42E]">关注</span>
                      </div>
                      <div v-else>
                        <span class="inline-block bg-[#FF4A34] w-[7px] h-[7px] rounded-half"></span>
                        <span class="ml-[8px] text-[#FF4A34]">预警</span>
                      </div>
                    </div>
                    <div v-else-if="item.property == 'status'" :style="{ color: scope.row[item.property] == '已处置' ? '#62D543' : '#FF4A34' }">
                      {{ scope.row[item.property] }}
                    </div>
                    <div v-else>{{ scope.row[item.property] }}</div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </dialog-fixed>
    <!-- 升降机信息   -->
    <dialog-fixed :visible.sync="elevatorDetailVisible" width="1180px" height="784px" closeIconTop="24px" closeIconRight="10px" @close="resetElevatorInfo">
      <div class="flex items-center justify-center w-full h-[36px] mt-[32px]">
        <img src="../assets/images/equipment/elevator-info.png" class="w-[575px]" alt="" />
      </div>
      <div style="height: calc(100% - 99px - 52px)" class="mt-[27px] ml-[44px] mr-[36px]" v-loading="isLoaidngEvatlor">
        <div class="h-[412px]">
          <div class="flex justify-between h-[89px]">
            <div class="w-[500px] mt-[8px]">
              <div class="flex">
                <div class="text-[21px] text-[#FFF]">{{ elevatorDetail?.name }}</div>
                <div class="text-[14px] text-[#FFF] ml-[22px] mt-[7px]">{{ elevatorDetail?.user_name + " " + elevatorDetail?.user_tel }}</div>
              </div>
              <div class="text-[14px] text-[hsla(0,0%,100%,0.5)] mt-[12px]">{{
                `保养周期：${elevatorDetail?.service_interval}天 | 下次保养时间：${elevatorDetail?.service_date}（剩余${elevatorDetail?.service_next}天）`
              }}</div>
            </div>
            <div class="flex w-[160px] text-[16px] text-[#FFF]">
              <div class="mt-[34px]">安全分</div>
              <div id="elevatorDetailScoreChart" class="w-[80px] h-[80px] ml-[24px]"></div>
            </div>
          </div>
          <div class="flex h-[323px]">
            <div class="w-[270px] h-full">
              <div class="flex h-[34px]">
                <div style="line-height: 34px" class="w-[108px] h-[34px] text-center bg-[#1E5FD5] rounded text-[#FFF] text-[15px]">工作参数</div>
              </div>
              <div class="relative mt-[14px]">
                <div class="bg-[#252F38] w-[110px] h-[253px] rounded-md"></div>
                <div class="absolute top-0 left-0 h-[253px] w-full overflow-auto">
                  <div v-for="(item, index) in workingParameters" class="workParameters" :class="{ activated: item.code === activatedParamCode }" :key="index">
                    <div class="w-[130px] text-[#FFF] pl-[14px]">{{ item.name }}</div>
                    <div class="w-[90px] text-[#FFF]">{{ item.val + item.unit }}</div>
                    <div class="examine" @click="examineList(item, 'elevatorWorkingParameterChart')">查看</div>
                  </div>
                </div>
              </div>
            </div>
            <div style="width: calc(100% - 270px)" class="h-full">
              <div id="elevatorWorkingParameterChart" class="ml-[30px] h-full"></div>
            </div>
          </div>
        </div>
        <div style="height: calc(100% - 412px)" class="flex">
          <div class="w-1/2 h-full pl-[20px]">
            <div class="w-[326] h-[27px]">
              <img src="../assets/images/equipment/alarm-information.png" alt="" class="h-full" />
            </div>
            <div style="height: calc(100% - 27px - 20px)" class="mt-[20px] overflow-auto">
              <template v-if="elevatorAlarmArr.length < 1">
                <div class="flex justify-center items-center w-full h-full">
                  <div class="w-[50px] h-[82px]">
                    <img src="../assets/images/equipment/no-record.png" class="w-[41px] h-[53px]" alt="" />
                    <div class="text-[hsla(0,0%,100%,0.5)] text-[12px] mt-[14px]">暂无数据</div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div v-for="(item, index) in elevatorAlarmArr" class="flex items-center h-[55px]" :key="index">
                  <div class="w-[73px] h-[30px]">
                    <div class="text-[14px] text-[#FFF] text-center">{{ $dayjs(item.time).format("HH:mm:ss") }}</div>
                    <div class="text-[10px] text-[hsla(0,0%,100%,0.4)] text-center">{{ $dayjs(item.time).format("YYYY/MM/DD") }}</div>
                  </div>
                  <div class="relative w-[1px] h-full bg-[#FFF] ml-[24px]">
                    <div class="withGradientMiddleBG"></div>
                  </div>
                  <div class="ml-[29px] w-[262px] text-[14px] text-[#FFF] font-semibold">{{ item.content }}</div>
                  <div class="ml-[4px]">
                    <span class="inline-block bg-[#FF4A34] w-[7px] h-[7px] rounded-half"></span>
                    <span class="ml-[8px] text-[#FF4A34]">预警</span>
                  </div>
                  <div
                    :style="{ 'background-color': item.status == 1 ? '#EF424E' : '#5D6A7C' }"
                    style="line-height: 39px"
                    class="ml-[26px] w-[51px] h-[39px] rounded-[6px] text-xs text-[#FFF] text-center font-semibold"
                    >{{ item.status == 1 ? "未处置" : "已处置" }}</div
                  >
                </div>
              </template>
            </div>
          </div>
          <div class="w-1/2 h-full">
            <div class="h-[27px]">
              <img src="../assets/images/equipment/risk-information.png" alt="" class="h-full" />
            </div>
            <div style="height: calc(100% - 27px - 26px)" class="mt-[26px] ml-[31px]">
              <div v-if="elevatorResk.length < 1" class="flex justify-center items-center w-full h-full">
                <div class="w-[50px] h-[82px]">
                  <img src="../assets/images/equipment/no-record.png" class="w-[41px] h-[53px]" alt="" />
                  <div class="text-[hsla(0,0%,100%,0.5)] text-[12px] mt-[14px]">暂无数据</div>
                </div>
              </div>
              <el-table v-else :data="elevatorResk" size="medium" height="100%" style="width: 100%">
                <el-table-column
                  v-for="item in towerCraneReskTitle"
                  :key="item.label"
                  :min-width="item.width"
                  :label="item.label"
                  :prop="item.property"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <div v-if="item.property == 'level'">
                      <div v-if="scope.row[item.property] == '1'">
                        <span class="inline-block bg-[#1988E8] w-[7px] h-[7px] rounded-half"></span>
                        <span class="ml-[8px] text-[#1988E8]">提示</span>
                      </div>
                      <div v-else-if="scope.row[item.property] == '2'">
                        <span class="inline-block bg-[#FFA42E] w-[7px] h-[7px] rounded-half"></span>
                        <span class="ml-[8px] text-[#FFA42E]">关注</span>
                      </div>
                      <div v-else>
                        <span class="inline-block bg-[#FF4A34] w-[7px] h-[7px] rounded-half"></span>
                        <span class="ml-[8px] text-[#FF4A34]">预警</span>
                      </div>
                    </div>
                    <div v-else-if="item.property == 'status'" :style="{ color: scope.row[item.property] == '已处置' ? '#62D543' : '#FF4A34' }">
                      {{ scope.row[item.property] }}
                    </div>
                    <div v-else>{{ scope.row[item.property] }}</div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </dialog-fixed>
    <!--塔群监控-->
    <dialog-fixed
      :visible.sync="towerGroupMonitorVisible"
      width="1180px"
      height="784px"
      closeIconTop="24px"
      closeIconRight="10px"
      @open="onTowerGroupOpen"
      @close="onTowerGroupClose"
    >
      <div class="flex items-center justify-center w-full h-[36px] mt-[32px]">
        <img src="../assets/images/equipment/tower_group_monitor.png" class="w-[575px]" alt="" />
      </div>
      <div @click="towerGroupMonitorVisible = false" class="absolute top-[39px] left-[28px] w-[84px] h-[15px] flex items-center cursor-pointer">
        <img src="../assets/images/back-icon.png" alt="" class="w-[16px] h-[15px]" />
        <span class="ml-[9px] text-[14px] text-white">返回</span>
      </div>
      <div style="height: calc(100% - 99px - 66px)" class="mt-[27px] ml-[44px] mr-[36px]">
        <div class="flex h-[480px]">
          <div class="w-1/2">
            <div class="relative mt-[130px] ml-[20px] w-[456px] h-[248px]">
              <div class="absolute top-0 left-0 w-[248px] h-[248px] border-[#1CFFCF] border-dashed border rounded-[50%] flex justify-center items-center">
                <div class="border-[#FFA42E] border-solid border-[2px] w-[15px] h-[10px] bg-[hsla(34,100%,59%,0.5)]"></div>
                <img src="../assets/images/equipment/corner-icon.png" class="absolute w-[105px] h-[124px] left-[70px] top-0" alt="" />
              </div>
              <div class="absolute top-0 right-0 w-[248px] h-[248px] border-[#1CFFCF] border-dashed border rounded-[50%] flex justify-center items-center">
                <div class="border-[#FFA42E] border-solid border-[2px] w-[15px] h-[10px] bg-[hsla(34,100%,59%,0.5)]"></div>
                <img
                  src="../assets/images/equipment/corner-icon.png"
                  style="transform: rotate(45deg); transform-origin: 50% 100%"
                  class="absolute w-[105px] h-[124px] left-[70px] top-0"
                  alt=""
                />
              </div>
            </div>
          </div>
          <div class="relative w-1/2">
            <img src="../assets/images/equipment/tower-crane-img.png" class="absolute top-[30px] left-[114px] w-[328px] h-[307px]" alt="" />
            <img src="../assets/images/equipment/tower-crane-img.png" class="absolute top-[100px] left-0 w-[328px] h-[329px]" alt="" />
            <img src="../assets/images/equipment/camera.png" alt="" class="absolute top-[68px] left-[143px] w-[27px] h-[27px]" />
            <img src="../assets/images/equipment/camera.png" alt="" class="absolute top-[68px] left-[378px] w-[27px] h-[27px]" />
            <img src="../assets/images/equipment/camera.png" alt="" class="absolute top-[144px] left-[29px] w-[27px] h-[27px]" />
            <img src="../assets/images/equipment/camera.png" alt="" class="absolute top-[144px] left-[264px] w-[27px] h-[27px]" />
            <div class="absolute top-[350px] left-[192px] w-[50px] text-[#FFF] text-[15px]">2#塔吊</div>
            <div class="absolute top-[438px] left-[76px] w-[50px] text-[#FFF] text-[15px]">1#塔吊</div>
          </div>
        </div>
        <div style="height: calc(100% - 480px)">
          <div class="h-[42px] flex">
            <div class="h-[42px] w-[97px] bg-[hsla(0,0%,100%,0.1)]"></div>
            <div v-for="(item, index) in towerCraneTitle" :key="index" class="flex items-center h-[42px] w-[162px] bg-[#414A5D] ml-[3px]">
              <img :src="getImageUrl(`../assets/images/equipment/${item.img}.png`)" class="w-[25px] ml-[42px]" alt="" />
              <span class="text-[16px] text-[#FFF] ml-[8px]">{{ item.title }}</span>
            </div>
          </div>
          <div v-for="(tc, index) in towerCraneValue" :key="index" class="flex h-[42px] mt-[3px]">
            <div style="line-height: 42px" class="h-[42px] w-[97px] bg-[#1A50C2] text-center text-[15px] text-[#FFF]">{{ tc.name }}</div>
            <div
              v-for="(item, i) in towerCraneTitle"
              :key="i"
              style="line-height: 42px"
              class="h-[42px] w-[162px] bg-[hsla(0,0%,100%,0.1)] ml-[3px] text-center text-[17px] text-[#FFF]"
            >
              {{ tc[item.key] }}
            </div>
          </div>
        </div>
      </div>
    </dialog-fixed>
  </div>
</template>

<script>
  import { EquipmentAPI } from "../api/equipment";
  import { mapGetters } from "vuex";
  export default {
    name: "Equipment",
    data() {
      return {
        customColor: "#FDC573",
        customWidth: 10,
        towerGroupMonitorVisible: false,
        enclosureRecordVisible: false,
        listTitle: [
          { property: "enclosureCode", label: "围挡编号", width: 510 },
          { property: "alarm", label: "报警信息", width: 165 },
          { property: "time", label: "时间", width: 170 },
        ],
        enclosureAlarmList: [],
        towerCraneVisible: false, //弹窗塔吊是否可见
        timerForTowerRelatedData: null, //塔吊弹窗中,获取塔吊报警、风险的定时器
        towerCraneReskTitle: [
          { property: "content", label: "风险项", width: 178 },
          { property: "level", label: "风险级别", width: 80 },
          { property: "time", label: "时间", width: 170 },
          { property: "status", label: "处置状态", width: 80 },
        ],
        towerCraneResk: [],
        towerCraneAlarmArr: [],
        tcWorkingParameters: [],
        workingParameters: [],
        elevatorDetailVisible: false,
        elevatorAlarmArr: [],
        elevatorResk: [],
        equipmentAlarmList: [],
        equipmentAlarmTotal: null,
        hereEquipmentList: [],
        isLoadingHereEquipmentInfo: false,
        isLoadingEquipmentWarningTotal: false,
        isLoadingEquipmentWarningList: false,
        towerCraneTitle: [
          { img: "moment", title: "力矩(%)", key: "moment" },
          { img: "weight", title: "吊重(t)", key: "load" },
          { img: "wind-speed", title: "风速(m/s)", key: "windSpeed" },
          { img: "luff", title: "幅度(°)", key: "radius" },
          { img: "height", title: "高度(m)", key: "height" },
          { img: "corner", title: "角度(°)", key: "angle" },
        ],
        towerCraneValue: [],
        elevatorList: [],
        towerCraneList: [],
        enclosureList: [],
        elevatorDetail: null,
        towerCraneDetail: null,

        chartInstances: [],
        fitableWarningNum: -1,
        isLoaidngEvatlor: false, //弹窗升降机加载标志
        isLoadingTower: false, //弹窗塔吊加载标志
        elevatorActivatedId: "", //激活的升降机Id
        pageElevatorDetailInfo: null,
        isLoadingElevatorDetailInfo: false, //升降机信息加载标志
        isLoadingTowerCraneList: false, //塔吊安全分echart加载标志
        isLoadingElevatorList: false, //升降机列表加载标志
        isLoadingEnclosureList: false, //围挡列表加载标志
        activatedParamCode: "", //当前查看的升降机参数
        workParamChart: null, //当前参数对应的echart实例
      };
    },
    computed: {
      equipmentAlarmListForView() {
        return this.equipmentAlarmList?.slice(0, 3) ?? [];
      },
      availableEquipmentInfo() {
        const types = ["围挡", "塔吊", "升降机"];
        const result = { total: 0, details: [] };
        const { hereEquipmentList } = this;
        types.forEach((type) => {
          const item = hereEquipmentList.find((item) => item.name === type);
          if (item) {
            result.total += item.count;
            result.details.push(item);
          } else {
            result.details.push({
              name: type,
              count: 0,
            });
          }
        });
        return result;
      },
      elevatorActivated() {
        return this.elevatorList.find((item) => item.id === this.elevatorActivatedId);
      },
      ...mapGetters("common", ["currentProjectId", "equipmentsColorMap", "activatedThirdSubmenu", "activatedThirdMenu"]),
    },
    watch: {
      currentProjectId: {
        immediate: true,
        handler(cv) {
          cv && this.updateThePage();
        },
      },
    },
    mounted() {
      // const target = window.scene.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^11718");
      // target.setColor("red", 1);
      // window.scene.execute("color", { objectIDs: ["6c84ed8d-7e10-6538-4deb-e1db9f1f0894^11718"], color: "red" });
      // window.scene.render();
    },
    beforeDestroy() {
      this.dispose();

      //升降机取消高亮
      let targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12055");
      targetObject && (targetObject.selected = false);

      targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12056");
      targetObject && (targetObject.selected = false);

      clearTimeout(this.timerForTowerRelatedData);

      //塔吊取消高亮
      // targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^11718");
      // targetObject && (targetObject.selected = false);

      // targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12003");
      // targetObject && (targetObject.selected = false);
    },
    methods: {
      //塔群监控打开时的处理逻辑
      async onTowerGroupOpen() {
        this.towerCraneValue = [];
        let res = await EquipmentAPI.getTowerCraneLastestData();
        res = res?.code === 0 ? res.data : null;
        if (res) {
          this.towerCraneValue = res.map((item, index) => {
            const result = { name: `${index + 1}#塔吊` };
            const data = item.data;
            if (item.factory.toUpperCase() === "PM") {
              result.moment = data.MomentPer;
              result.load = data.load;
              result.windSpeed = data.WindSpeed.toFixed(2);
              result.radius = data.Radius.toFixed(2);
              result.height = data.Height;
              result.angle = data.Angle;
            } else {
              result.moment = data.Moment;
              result.load = data.Weight;
              result.windSpeed = data.WindSpeed.toFixed(2);
              result.radius = Number(data.RRange).toFixed(2);
              result.height = data.Height;
              result.angle = data.Angle;
            }
            return result;
          });
        }
      },
      //清空塔群监控相关信息
      onTowerGroupClose() {
        this.towerCraneValue = [];
      },
      //更新页面
      updateThePage() {
        this.dispose();
        this.updateTowerCraneList();
        this.updateElevatorList();
        this.updateEnclosureList();
        this.updateEquipmentAlarmTotal();
        this.updateEquipmentAlarmList();
        this.updateHereEquipmentsInfo();
      },
      //释放
      dispose() {
        this.chartInstances.forEach((chart) => {
          chart.dispose();
        });
        this.chartInstances = [];
      },

      //更新塔吊列表7
      async updateTowerCraneList() {
        this.isLoadingTowerCraneList = true;
        const res = await EquipmentAPI.getEquipmentList({ id: "7" });
        this.isLoadingTowerCraneList = false;
        this.towerCraneList = (res?.code === 0 ? res.data : []).slice(0, 2); //塔吊列表
        this.updateTowerCraneEcharts();
      },
      //渲染页面塔吊安全分echart
      updateTowerCraneEcharts() {
        this.$nextTick(() => {
          this.towerCraneList.forEach((item) => {
            this.showTowerSecureChart("TowerChart" + item.id, item.score);
          });
        });
      },

      //更新升降机列表6
      async updateElevatorList() {
        this.isLoadingElevatorList = true;
        this.elevatorList = [];
        const res = await EquipmentAPI.getEquipmentList({ id: "6" }); // 升降机
        this.isLoadingElevatorList = false;
        this.elevatorList = (res?.code === 0 ? res.data : []).slice(0, 2); //升降机列表
        if (this.elevatorList.length) {
          this.elevatorActivatedId = this.elevatorList[0].id;
          this.getElevatorInfoActivated();
        }
      },
      //获取选中的升降机的信息
      async getElevatorInfoActivated() {
        if (this.elevatorActivatedId) {
          this.isLoadingElevatorDetailInfo = true;
          this.pageElevatorDetailInfo = null;
          const res = await EquipmentAPI.getSpecifyEquipmentLift({ id: this.elevatorActivatedId });
          this.pageElevatorDetailInfo = res?.code === 0 ? res.data : null;
          this.isLoadingElevatorDetailInfo = false;
        }
      },
      //响应点击升降机事件
      onClickElevator(item) {
        if (this.isLoadingElevatorDetailInfo || (item && item?.id === this.elevatorActivatedId)) return;
        this.elevatorActivatedId = item.id;
        if (this.elevatorActivatedId === 401) {
          let targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12055");
          targetObject?.fit();
          targetObject && (targetObject.selected = true);

          targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12056");
          targetObject && (targetObject.selected = false);
        } else if (this.elevatorActivatedId === 402) {
          let targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12056");
          targetObject?.fit();
          targetObject && (targetObject.selected = true);

          targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12055");
          targetObject && (targetObject.selected = false);
        }
        this.getElevatorInfoActivated();
      },

      //更新围挡列表10
      async updateEnclosureList() {
        this.isLoadingEnclosureList = true;
        this.enclosureList = [];
        const res = await EquipmentAPI.getEquipmentList({ id: "10" }); // 围挡;
        this.isLoadingEnclosureList = false;
        this.enclosureList = res?.code === 0 ? res.data : []; //围挡列表
      },

      //更新累计报警、未处置报警
      async updateEquipmentAlarmTotal() {
        this.isLoadingEquipmentWarningTotal = true;
        this.equipmentAlarmTotal = null;
        const res = await EquipmentAPI.getEquipmentAlarmTotal();
        this.isLoadingEquipmentWarningTotal = false;
        this.equipmentAlarmTotal = res?.code === 0 ? res.data : null;
        this.setZoomFactor(this.equipmentAlarmTotal?.datas);
      },

      //更新设备报警时间线
      async updateEquipmentAlarmList() {
        this.isLoadingEquipmentWarningList = true;
        this.equipmentAlarmList = [];
        const res = await EquipmentAPI.getEquipmentAlarmList();
        this.isLoadingEquipmentWarningList = false;
        this.equipmentAlarmList = res?.code === 0 ? res.data : [];
      },

      //更新在场设备echart
      async updateHereEquipmentsInfo() {
        this.isLoadingHereEquipmentInfo = true;
        this.hereEquipmentList = [];
        const res = await EquipmentAPI.getHereEquipmentList();
        this.isLoadingHereEquipmentInfo = false;
        this.hereEquipmentList = res?.code === 0 ? res.data : [];
        this.showHereEquChart();
      },

      //更新塔吊安全分echart
      showTowerSecureChart(id, score) {
        const chartDom = document.getElementById(id);
        const chartInstance = this.$echarts.getInstanceByDom(chartDom);
        chartInstance?.dispose();
        if (chartDom) {
          const myChart = this.$echarts.init(chartDom);
          const option = {
            graphic: {
              elements: [
                {
                  type: "text",
                  left: "center",
                  top: "middle",
                  style: {
                    text: score,
                    fontSize: 20,
                    fill: "#FFF",
                  },
                },
              ],
            },
            series: [
              {
                type: "pie",
                radius: ["70%", "85%"],
                avoidLabelOverlap: false,
                left: 0,
                right: 0,
                width: "100%",
                labelLine: { show: false },
                data: [
                  {
                    value: score,
                    itemStyle: {
                      //调用方法进行范围计算
                      color: score < 60 ? "#FF4A34" : "rgba(255,198,87,1)",
                    },
                  },
                  {
                    value: 100 - score,
                    itemStyle: {
                      color: "rgba(255,198,87,0.1)",
                    },
                  },
                ],
              },
            ],
          };
          myChart.setOption(option);
          this.chartInstances.push(myChart);
        }
      },

      //设置设备报警条缩放因子
      setZoomFactor(datas) {
        if (datas) {
          const maxFitable = 58;
          const counts = datas.map((item) => item.count);
          const maxCountInArray = Math.max(...counts);
          this.zoomFactor = (maxFitable * 0.75) / maxCountInArray;
          //让数组中的最大值警告条数，占总宽度的75%，无论该值是否大于maxFitable
        } else {
          this.zoomFactor = 0;
        }
      },

      //todo 关闭塔吊弹窗时重置弹窗内用到的变量
      resetTowerCraneInfo() {
        // console.log("重置一些值");
        clearTimeout(this.timerForTowerRelatedData);
        this.timerForTowerRelatedData = null;
        this.towerCraneDetail = [];
        this.towerCraneResk = [];
        this.towerCraneAlarmArr = [];
        this.tcWorkingParameters = [];
        this.activatedParamCode = "";
        this.workParamChart?.dispose();
      },

      //todo 关闭升降机弹窗时重置弹窗内用到的变量
      resetElevatorInfo() {
        this.elevatorDetail = [];
        this.elevatorResk = [];
        this.elevatorAlarmArr = [];
        this.workingParameters = [];
        this.activatedParamCode = "";
        this.workParamChart?.dispose();
      },

      //显示塔吊弹窗
      async openTowerCraneDetail(item) {
        if (!item) return;
        if (item.id === 400) {
          let targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12003");
          targetObject?.fit();
          // targetObject && (targetObject.selected = true);

          // targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^11718");
          // targetObject && (targetObject.selected = false);
        }
        if (item.id === 389) {
          let targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^11718");
          targetObject?.fit();
          // targetObject && (targetObject.selected = true);

          // targetObject = window.scene?.findObject("6c84ed8d-7e10-6538-4deb-e1db9f1f0894^12003");
          // targetObject && (targetObject.selected = false);
        }
        this.towerCraneVisible = true;
        this.isLoadingTower = true;
        await Promise.all([
          this.updateEquipmentDetail(item, "TowerCrane"),
          this.updateEquipmentRisk(item, "TowerCrane"),
          this.updateEquipmentAlarm(item, "TowerCrane"),
          this.updateEquipmentWorkingParameters(item, "TowerCrane"),
        ]);
        this.setFetchingTowerRelatedData(); //设置获取塔吊相关信息的定时器
        this.isLoadingTower = false;
      },
      setFetchingTowerRelatedData() {
        if (!this.timerForTowerRelatedData) {
          this.timerForTowerRelatedData = setTimeout(this.fetchTowerRelatedData, 30 * 1000);
        }
      },
      fetchTowerRelatedData() {
        this.timerForTowerRelatedData = null;
        EquipmentAPI.getTaDiaoHaveRisk()
          .then((res) => {
            let result = res?.code === 0 ? res.data : [];
            // console.log(result,"bool值,如何应用");
            // this.towerCraneAlarmArr = result;
            this.setFetchingTowerRelatedData();
          })
          .catch(() => {
            clearTimeout(this.timerForTowerRelatedData);
            this.timerForTowerRelatedData = null;
          });
      },

      //显示升降机弹窗
      async openElevatorDetail(item) {
        if (!item) return;
        this.elevatorDetailVisible = true;
        this.isLoaidngEvatlor = true;
        await Promise.all([
          this.updateEquipmentDetail(item, "elevator"),
          this.updateEquipmentRisk(item, "elevator"),
          this.updateEquipmentAlarm(item, "elevator"),
          this.updateEquipmentWorkingParameters(item, "elevator"),
        ]);
        this.isLoaidngEvatlor = false;
      },

      //更新设备详情
      async updateEquipmentDetail(item, type) {
        const res = await EquipmentAPI.getSpecifyEquipmentDetail({ id: item.id });
        if (type == "TowerCrane") {
          this.towerCraneDetail = res?.code === 0 ? res.data : [];
          this.showTowerSecureChart("towerDetailScoreChart", this.towerCraneDetail.score);
        } else {
          this.elevatorDetail = res?.code === 0 ? res.data : [];
          this.showTowerSecureChart("elevatorDetailScoreChart", this.elevatorDetail.score);
        }
      },
      //更新设备风险
      async updateEquipmentRisk(item, type) {
        const res = await EquipmentAPI.getSpecifyEquipmentRisk({ id: item.id });
        if (type == "TowerCrane") {
          this.towerCraneResk = res?.code === 0 ? res.data : [];
        } else {
          this.elevatorResk = res?.code === 0 ? res.data : [];
        }
      },
      //更新设备报警
      async updateEquipmentAlarm(item, type) {
        const res = await EquipmentAPI.getSpecifyEquipmentAlarm({ id: item.id });
        if (type == "TowerCrane") {
          this.towerCraneAlarmArr = res?.code === 0 ? res.data : [];
        } else {
          this.elevatorAlarmArr = res?.code === 0 ? res.data : [];
        }
      },
      //更新设备工作参数、结构参数
      async updateEquipmentWorkingParameters(item, type) {
        let res;
        if (type === "TowerCrane") {
          res = await EquipmentAPI.getSpecifyEquipment({ id: item.id });
        } else {
          res = await EquipmentAPI.getSpecifyEquipmentLift2({ id: item.id });
        }
        if (type == "TowerCrane") {
          const result = res?.code === 0 ? res.data.basic : [];
          result.forEach((item) => {
            item.val = Number(item.val).toFixed(2);
          });
          this.tcWorkingParameters = result;
          this.showWorkParametersChart(this.tcWorkingParameters.length > 0 ? this.tcWorkingParameters[0] : null, "tcWorkingParameterChart");
        } else {
          this.workingParameters = res?.code === 0 ? res.data.sensors : [];
          this.showWorkParametersChart(this.workingParameters.length > 0 ? this.workingParameters[0] : null, "elevatorWorkingParameterChart");
        }
      },

      examineList(item, elementID) {
        this.showWorkParametersChart(item, elementID);
      },
      showWorkParametersChart(item, elementID) {
        if (item == null) {
          return;
        }
        this.activatedParamCode = item.code;
        const chartDom = document.getElementById(elementID);
        const chartInstance = this.$echarts.getInstanceByDom(chartDom);
        chartInstance?.dispose();
        const myChart = this.$echarts.init(chartDom);
        const xData = item.x_axis;
        let data = item.list.map((val) => {
          return parseFloat(val);
        });
        let inputFlowData = data;
        const option = {
          color: ["#e75d4d"],
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              label: {
                backgroundColor: "#6a7985",
              },
            },
            valueFormatter(value) {
              return Number(value).toFixed(2);
            },
          },
          legend: {
            data: [item.name],
            top: "15%",
            itemGap: 64,
            textStyle: {
              color: "#fff",
              fontSize: 14,
            },
          },
          grid: {
            left: 36,
            right: 36,
            bottom: 40,
          },
          xAxis: [
            {
              type: "category",
              boundaryGap: true,
              data: xData,
              axisTick: { show: false },
              axisLine: { show: false },
              axisLabel: {
                textStyle: {
                  fontSize: 10,
                  color: "#AAAAAA",
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              axisLine: { show: false },
              axisLabel: {
                // formatter: '{value} kg',
                textStyle: {
                  fontSize: 10,
                  color: "#AAAAAA",
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "rgba(229, 229, 229, 0.1)",
                  width: 1,
                  type: "solid",
                },
              },
            },
          ],
          series: [
            {
              name: item.name,
              type: "line",
              smooth: true,
              lineStyle: {
                width: 3,
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#533b41",
                  },
                  {
                    offset: 1,
                    color: "#191728",
                  },
                ]),
              },
              emphasis: {
                focus: "series",
              },
              data: inputFlowData,
            },
          ],
          dataZoom: [{ type: "inside" }],
        };
        myChart.setOption(option);
        this.workParamChart = myChart;
      },

      enclosureAlarmRecord() {
        this.enclosureRecordVisible = true;
      },
      resetEnclosureAlarmList() {
        this.enclosureAlarmList = [];
      },
      async onEnclosureArarmListOpen() {
        const res = await EquipmentAPI.getEnclosureAlarmList({ wdGroupId: 1 });
        this.enclosureAlarmList = (res?.code === 0 ? res.data : []).map((item) => {
          return {
            enclosureCode: item.wdName,
            alarm: "离线",
            time: item.lostTimeStart,
          };
        });
      },
      formatYingLi(v) {
        return `${v}Mpa`;
      },
      showHereEquChart() {
        const chartDom = document.getElementById("hereEquChart");
        if (chartDom) {
          const myChart = this.$echarts.init(chartDom);
          const option = {
            legend: {
              top: "bottom",
              icon: "circle",
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
            },
            legend: {
              show: false,
            },
            series: [
              {
                type: "pie",
                radius: ["35%", "90%"],
                center: ["50%", "55%"],
                left: 0,
                top: 0,
                startAngle: 120,
                roseType: "area",
                itemStyle: {
                  borderRadius: 2,
                },
                label: {
                  color: "#fff",
                },
                data: this.availableEquipmentInfo.details.map((item) => {
                  return {
                    value: item.count,
                    name: item.name,
                    itemStyle: {
                      color: this.equipmentsColorMap[item.name],
                    },
                  };
                }),
              },
            ],
          };
          myChart.setOption(option);
          this.chartInstances.push(myChart);
        }
      },

      //响应设备报警点击事件
      onDeviceWarningClick(status) {
        this.$store.commit("common/setArgForGetRiskData", { page: 1, limit: 10, name: "", status: status, warningType: "2", upgradeType: "", riskType: "3" });
        this.$store.commit("common/setIsNotificationListVisible", true);
      },

      getImageUrl(path) {
        return new URL(path, import.meta.url).href;
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::v-deep .roundedMaskFlag > .el-loading-mask {
    @apply rounded-tl-[34px] rounded-bl-[34px];
  }
  ::v-deep .el-timeline.eltimelineCus {
    .el-timeline-item {
      padding-bottom: 30px;
      margin-left: -4px;
      .el-timeline-item__tail {
        position: absolute;
        left: 4.5px;
        height: 100%;
        border-left: 1px solid hsla(0, 0%, 100%, 0.2);
      }
    }
    .el-timeline-item:last-of-type {
      padding-bottom: 0;
    }
  }
  .viewMainBG {
    background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9), hsla(236, 33%, 18%, 0.9));
  }
  .elevatorBG {
    background: url("../assets/images/equipment/elevator-bg.png") no-repeat left center / cover border-box;
  }
  .elevatorItem {
    &.activated {
      background: #1e5fd5;
    }
  }
  .offline {
    background-color: #ef424e;
  }
  .alarmBG {
    background: url("../assets/images/equipment/alarm-bg.png") no-repeat left center/cover border-box;
  }
  .hereNumsBG {
    background: url("../assets/images/equipment/equipment-type-bg.png") no-repeat left center/cover border-box;
  }
  ::v-deep .el-progress {
    .el-progress-bar {
      width: calc(100% - 68px);
    }
    .el-progress__text {
      float: right;
      min-width: 68px;
      font-size: 14px !important;
      color: #fff !important;
      text-align: left;
    }
    .el-progress-bar__outer {
      width: calc(100% + 36px);
      background-color: hsla(36, 97%, 72%, 0.1) !important;
    }
  }
  .withGradientBG {
    $size: 1.5rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-7px, -50%, 0);
    width: $size;
    height: $size;
    border-radius: 50%;
    background: radial-gradient(circle calc($size / 3) at 50% 50%, #fff, #fff 50%, hsla(240, 5%, 40%, 0.2) calc(50% + 1px), hsla(240, 5%, 40%, 0.2) 100%)
      no-repeat center/cover border-box;
  }
  .withGradientMiddleBG {
    $size: 1.5rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-12px, -50%, 0);
    width: $size;
    height: $size;
    border-radius: 50%;
    background: radial-gradient(circle calc($size / 3) at 50% 50%, #fff, #fff 50%, hsla(240, 5%, 40%, 0.2) calc(50% + 1px), hsla(240, 5%, 40%, 0.2) 100%)
      no-repeat center/cover border-box;
  }
  .workParameters {
    display: flex;
    align-items: center;
    height: 31px;
    font-size: 13px;
    &:hover,
    &.activated {
      background-color: #1988e8;
      .examine {
        color: #fff;
      }
    }
    .examine {
      color: #88898b;
      cursor: pointer;
    }
  }
</style>
