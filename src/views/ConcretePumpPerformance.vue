<template>
  <div class="viewWrapper">
    <collapse direction="right" class="absolute z-50 right-0 top-[127px]">
      <div class="viewMainBG w-[734px] h-[856px] px-[24px] pt-[20px] rounded-tl-[12px] rounded-bl-[12px]" key="concretePump">
        <img src="../assets/images/concrete-pump-performance/concrete-pump-performance-bg.png" class="w-[328px] h-[28px]" alt="测点监控" key="1" />

        <!--温度-->
        <div class="mt-2">
          <div class="titleBG inline-flex mt-[22px] ml-[8px] w-[108px] h-[34px] pl-[14px] items-center font-pingfang-regular text-[16px]">
            <div class="mr-[6px]">温度</div>
            <div>°C</div>
          </div>
          <div class="flex mt-[32px] w-full h-[156px]" v-loading="isLoadingTemperatureData">
            <div class="grid grid-cols-2 gap-y-[32px] ml-[32px] w-[220px] h-[144px] flex-none">
              <div
                v-for="(item, i) in temperatureData.items"
                :key="i"
                class="text-center h-[56px]"
                :class="{
                  'border-r border-[hsla(0,0%,100%,0.1)] pr-[54px]': i % 2 === 0,
                  'pl-[54px]': i % 2 === 1,
                }"
              >
                <div class="text-[34px] font-ding -mt-[10px]">{{ item.value }}</div>
                <div class="text-[14px] font-pingfang-regular">{{ item.name }}</div>
              </div>
            </div>
            <div id="temperatureChart" class="ml-[48px] -mt-[32px] flex-1"></div>
          </div>
        </div>

        <!--压力-->
        <div class="mt-6">
          <div class="titleBG inline-flex mt-[22px] ml-[8px] w-[108px] h-[34px] pl-[14px] items-center font-pingfang-regular text-[16px]">
            <div class="mr-[6px]">压力</div>
            <div>MPa</div>
          </div>
          <div class="flex mt-[32px] w-full h-[156px]" v-loading="isLoadingPressureData">
            <div class="grid grid-cols-2 gap-y-[32px] ml-[32px] w-[220px] h-[144px] flex-none">
              <div
                v-for="(item, i) in pressureeData.items"
                :key="i"
                class="text-center h-[56px]"
                :class="{
                  'border-r border-[hsla(0,0%,100%,0.1)] pr-[54px]': i % 2 === 0,
                  'pl-[54px]': i % 2 === 1,
                }"
              >
                <div class="text-[34px] font-ding -mt-[10px]">{{ item.value }}</div>
                <div class="text-[14px] font-pingfang-regular">{{ item.name }}</div>
              </div>
            </div>
            <div id="pressureChart" class="ml-[48px] -mt-[32px] flex-1"></div>
          </div>
        </div>

        <!--加速度-->
        <div class="mt-6">
          <div class="titleBG inline-flex mt-[22px] ml-[8px] w-[108px] h-[34px] pl-[14px] items-center font-pingfang-regular text-[16px]">
            <div class="mr-[6px]">加速度</div>
            <div>m/s²</div>
          </div>
          <div class="flex mt-[32px] w-full h-[156px]" v-loading="isLoadingSpeed">
            <div class="grid grid-cols-2 gap-y-[32px] ml-[32px] w-[220px] h-[144px] flex-none">
              <div
                v-for="(item, i) in speedData.items"
                :key="i"
                class="text-center h-[56px]"
                :class="{
                  'border-r border-[hsla(0,0%,100%,0.1)] pr-[54px]': i % 2 === 0,
                  'pl-[54px]': i % 2 === 1,
                }"
              >
                <div class="text-[34px] font-ding -mt-[10px]">{{ item.value }}</div>
                <div class="text-[14px] font-pingfang-regular">{{ item.name }}</div>
              </div>
            </div>
            <div id="speedChart" class="ml-[48px] -mt-[32px] flex-1"></div>
          </div>
        </div>
      </div>
    </collapse>
  </div>
</template>

<script>
  import { ConcretePumpPerformanceAPI } from "../api/concretePumpPerformance.js";
  import { mapGetters } from "vuex";

  const dirtStatusTemplate = `<div class="w-[382px] p-[20px] rounded-[12px] bg-[#383B4A]">
        <div class="flex justify-between items-center">
          <div class="font-pingfang-regular text-[20px]">管道中混凝土姿态</div>
          <img src="/images/close.png" class="isClose w-[16px] h-[16px] cursor-pointer" alt="混凝土姿态" />
        </div>
        <div class="mt-[16px]">
          <img src="/images/dirt-status.png" alt="混凝土姿态" />
        </div>
      </div>`;

  export default {
    data() {
      return {
        chartInstances: [],
        temperatureData: {
          items: null,
          chartData: null,
        },
        isLoadingTemperatureData: false,

        pressureeData: {
          items: null,
          chartData: null,
        },
        isLoadingPressureData: false,

        speedData: {
          items: null,
          chartData: null,
        },
        isLoadingSpeed: false,

        hintPontId: "",

        legendNames: ["1#", "2#", "3#", "4#"],
      };
    },
    computed: {
      ...mapGetters("common", ["currentProjectId", "isSceneLoadComplete", "concretePumpPerformanceColorMap"]),
    },
    watch: {
      currentProjectId: {
        immediate: true,
        handler(cv) {
          if (cv) {
            this.updateThePage();
            if (cv === "42") {
              this.showCar();
            } else {
              this.hideCar();
            }
          }
        },
      },
      isSceneLoadComplete: {
        immediate: true,
        handler(cv) {
          // console.log("watch-isSceneLoadComplete,concrete");
          cv && this.updateTheScene(true);
          this.addSupportPoints();
        },
      },
    },
    mounted() {
      this.exposeToWindow();
    },
    beforeDestroy() {
      this.resetAllHintPoint();
      this.removeSupportPoints();
      this.updateTheScene(false);
      this.disposeTheCharts();
      this.deleteExposeToWindow();
    },
    methods: {
      addSupportPoints() {
        this.addNumAnnotation([121.4291394266952, 31.195277766981576, 8.910571605164733], { id: `concrete-${this.$nanoid()}`, num: 1 });
        this.addNumAnnotation([121.42913978212624, 31.195277153950954, 52.635739257885675], { id: `concrete-${this.$nanoid()}`, num: 2 });
        this.addNumAnnotation([121.42913302445277, 31.195275334344657, 90.72671050467697], { id: `concrete-${this.$nanoid()}`, num: 3 });
        this.addNumAnnotation([121.42913484314118, 31.195275371498155, 137.8373051429267], { id: `concrete-${this.$nanoid()}`, num: 4 });
      },
      removeSupportPoints() {
        if (window.scene) {
          const reg = /^\s*concrete-/i;
          const scene = window.scene;
          window.scene.features.forEach((feature) => {
            if (reg.test(feature?.id + "")) {
              scene.removeFeature(feature.id, true);
            }
          });
        }
      },

      //添加hint二维标签
      addNumAnnotation(geographical, arg) {
        const scene = window.scene;
        if (scene) {
          const annotation = scene.addFeature("annotation", arg.id);
          annotation.origin = [geographical[0], geographical[1]];
          annotation.altitude = geographical[2];
          const postData = {
            position: { x: 0, y: 0, z: 0 }, //偏移量
            // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
            content: null,
            // 二维标签html，自定义html字符串
            htmlCode: `<div data-name="${arg.num}#" data-id="${arg.id}" class="monitorPoint relative w-[34px] h-[34px] text-[16px] flex justify-center items-center">${arg.num}#</div>`,
            // 二维标签样式设置，自定义css字符串
            // 如果二次添加的锚点样式相同，该参数可为空
            cssCode: `.monitorPoint{ border-radius: 100%; background-color:#4d64e1; border: 1px solid #fff;}`,
            // 二维标签js，自定义字符串
            //增加一个window.page里面包含可以公用的方法
            jsCode: `annotation.addEventListener("click",function(e){
              window.page.reactOnClick(e?.target?.dataset?.name);
              });`,
            // 自定义锚点类型，默认为default,自定义标签为custom
            setType: { anchorType: "custom" },
          };
          // 二维标签数据标识
          annotation.dataKey = "annotation-" + annotation.id;
          // 设置标签数据
          scene.postData(postData, annotation.dataKey);
          // 加载二维标签
          annotation.load();
        }
      },
      //展示特定的曲线
      reactOnClick(name) {
        const { legendNames } = this;
        this.chartInstances.forEach((myChart) => {
          if (myChart && name) {
            const targets = legendNames.filter((item) => item !== name);
            targets.forEach((item) => {
              myChart.dispatchAction({ type: "legendUnSelect", name: item });
            });
            myChart.dispatchAction({ type: "legendSelect", name });
          }
        });
        // const target = document.querySelector(`[data-name='${name}'`);
        // const id = target?.dataset?.id;
        // this.fit2Feature(id);
      },
      //响应legend图例选中改变事件
      onLegendSelectChanged(data) {
        const name = data.name;
        this.chartInstances.forEach((myChart) => {
          myChart.dispatchAction({ type: "legendSelect", name });
        });
        // const target = document.querySelector(`[data-name='${name}'`);
        // const id = target?.dataset?.id;
        // this.fit2Feature(id);
      },
      //释放echart实例
      disposeTheCharts() {
        this.chartInstances.forEach((item) => {
          item.dispose();
        });
        this.chartInstances = [];
      },

      //更新场景
      updateTheScene(isAdd) {
        if (isAdd) {
          this.showCar();
          this.addHintAnnotation([121.42911847455673, 31.195374563942167, 2.130553555548564], { id: `hint-${this.$nanoid()}` });
        } else {
          this.hideCar();
          this.removeHintAnnotation();
        }
      },

      //更新页面
      updateThePage() {
        this.disposeTheCharts();
        this.updatetemperatureChart();
        this.updatePressureChart();
        this.updateSpeedChart();
      },

      //温度图表
      async updatetemperatureChart() {
        this.isLoadingTemperatureData = true;
        let res = await ConcretePumpPerformanceAPI.getTemperatureData();
        this.isLoadingTemperatureData = false;
        const data = res?.code === 0 ? res.data : null;
        const items = [];
        const chartData = {};
        Object.keys(data).forEach((item) => {
          items.push({
            name: item,
            value: data[item].value,
          });
          chartData[item] = data[item].chartData;
        });
        this.temperatureData = { items, chartData };
        const chartDom = document.getElementById("temperatureChart");
        if (chartDom) {
          const myChart = this.$echarts.init(chartDom);
          const xData = Object.keys(chartData["1#"]); // 09:00 - 21:00
          const allData = [];
          const legendData = Object.keys(chartData);
          legendData.forEach((seriesName) => {
            Object.keys(chartData[seriesName]).forEach((key) => {
              allData.push(chartData[seriesName][key]);
            });
          });
          const markLineData = this.genMarkLineData(xData, allData);
          const that = this;
          const option = {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
              formatter(params) {
                return params
                  .map((item) => {
                    return `
                      <p>
                        <span style="display:inline-block;border-radius:10px;width:10px;height:10px;
                        background-color:${that.concretePumpPerformanceColorMap[item.seriesName]};"></span>
                        <span style="margin-left:4px">${item.seriesName}</span>
                        <span style="margin-left:4px">${item.name}</span>
                        <span style="margin-left:4px">${item.value} °C</span>
                      </p>`;
                  })
                  .join("<br/>");
              },
            },
            legend: {
              data: legendData,
              icon: "rect",
              top: 0,
              itemGap: 32,
              itemHeight: 2,
              itemWidth: 14,
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
            },
            grid: {
              top: 0,
              bottom: 18,
              right: 0,
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: xData,
                axisTick: { show: false },
                axisLine: { show: false },
                axisLabel: {
                  fontSize: 12,
                  color: "hsla(219, 52%, 81%, 0.5)",
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                show: true,
                axisTick: { show: false },
                splitLine: { show: false },
                splitNumber: 3,
                axisLabel: {
                  verticalAlign: "bottom",
                  fontSize: 12,
                  color: "hsla(219, 52%, 81%, 0.5)",
                },
              },
            ],
            series: Object.keys(chartData).map((key, i) => {
              const series = {
                name: key,
                color: this.concretePumpPerformanceColorMap[key],
                type: "line",
                smooth: true,
                lineStyle: { width: 2 },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#1e2135",
                    },
                    {
                      offset: 1,
                      color: "#452c45",
                    },
                  ]),
                },
                data: Object.keys(chartData[key]).map((key2) => chartData[key][key2]),
              };
              if (i === 0) {
                series.markLine = {
                  symbol: ["none", "none"],
                  lineStyle: {
                    color: "hsla(0,0%,100%,0.1)",
                    type: "solid",
                    width: 1,
                  },
                  data: markLineData,
                };
              }
              return series;
            }),
            dataZoom: [{ type: "inside" }],
          };
          myChart.setOption(option);
          myChart.on("legendselectchanged", this.onLegendSelectChanged);
          this.chartInstances.push(myChart);
        }
      },

      //生成温度图表的标记线数据
      genMarkLineData(xData, data) {
        const genData = [];
        const len = xData.length;
        const minNum = Math.min(...data);
        const maxNum = Math.max(...data);
        for (let index = 0; index < len; index++) {
          if (index === 0 || index === len - 1) continue;
          const element = xData[index];
          genData.push([{ coord: [element, minNum] }, { coord: [element, maxNum] }]);
        }
        return genData;
      },

      //压力图表
      async updatePressureChart() {
        this.isLoadingPressureData = true;
        let res = await ConcretePumpPerformanceAPI.getPressureData();
        this.isLoadingPressureData = false;
        const data = res?.code === 0 ? res.data : null;
        const { xDatas, chartData } = data;
        const items = [];
        Object.keys(chartData).forEach((key) => {
          items.push({
            name: key,
            value: chartData[key].value,
          });
        });
        this.pressureeData = { items, chartData };
        const chartDom = document.getElementById("pressureChart");
        if (chartDom) {
          const legendData = Object.keys(chartData).map((item) => {
            return {
              name: item,
              itemStyle: {
                color: this.concretePumpPerformanceColorMap[item],
              },
            };
          });
          const myChart = this.$echarts.init(chartDom);
          const that = this;
          const option = {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
              formatter(params) {
                return params
                  .map((item) => {
                    return `
                      <p>
                        <span style="display:inline-block;border-radius:10px;width:10px;height:10px;
                        background-color:${that.concretePumpPerformanceColorMap[item.seriesName]};"></span>
                        <span style="margin-left:4px">${item.seriesName}</span>
                        <span style="margin-left:4px">${item.name}</span>
                        <span style="margin-left:4px">${item.value.toFixed(2)} MPa</span>
                      </p>`;
                  })
                  .join("<br/>");
              },
            },
            legend: {
              data: legendData,
              icon: "rect",
              top: 0,
              itemGap: 32,
              itemHeight: 2,
              itemWidth: 14,
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
            },
            grid: {
              top: 24,
              bottom: 18,
              right: 0,
            },
            xAxis: {
              type: "category",
              data: xDatas,
              boundaryGap: false,
              axisTick: { show: false },
              axisLine: { show: false },
              axisLabel: {
                fontSize: 12,
                color: "hsla(219, 52%, 81%, 0.5)",
              },
            },
            yAxis: {
              type: "value",
              show: true,
              axisTick: { show: false },
              splitLine: { show: false },
              splitNumber: 3,
              axisLabel: {
                verticalAlign: "bottom",
                fontSize: 12,
                color: "hsla(219, 52%, 81%, 0.5)",
              },
            },

            dataZoom: [{ type: "inside" }],
            series: Object.keys(chartData).map((key) => {
              return {
                name: key,
                type: "line",
                smooth: true,
                showSymbol: false,
                itemStyle: { color: this.concretePumpPerformanceColorMap[key] },
                data: chartData[key].chartData,
              };
            }),
          };

          myChart.setOption(option);
          myChart.on("legendselectchanged", this.onLegendSelectChanged);
          this.chartInstances.push(myChart);
        }
      },

      //加速度图表
      async updateSpeedChart() {
        this.isLoadingSpeed = true;
        let res = await ConcretePumpPerformanceAPI.getSpeedData();
        this.isLoadingSpeed = false;
        const data = res?.code === 0 ? res.data : null;
        const { xDatas, chartData } = data;
        const items = [];
        Object.keys(chartData).forEach((key) => {
          items.push({
            name: key,
            value: chartData[key].value,
          });
        });
        this.speedData = { items, chartData };
        const chartDom = document.getElementById("speedChart");
        if (chartDom) {
          const legendData = Object.keys(chartData).map((item) => {
            return {
              name: item,
              itemStyle: {
                color: this.concretePumpPerformanceColorMap[item],
              },
            };
          });
          const myChart = this.$echarts.init(chartDom);
          const that = this;
          const option = {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
              formatter(params) {
                return params
                  .map((item) => {
                    return `
                      <p>
                        <span style="display:inline-block;border-radius:10px;width:10px;height:10px;
                        background-color:${that.concretePumpPerformanceColorMap[item.seriesName]};"></span>
                        <span style="margin-left:4px">${item.seriesName}</span>
                        <span style="margin-left:4px">${item.name}</span>
                        <span style="margin-left:4px">${item.value.toFixed(2)} MPa</span>
                      </p>`;
                  })
                  .join("<br/>");
              },
            },
            legend: {
              data: legendData,
              icon: "rect",
              top: 0,
              itemGap: 32,
              itemHeight: 2,
              itemWidth: 14,
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
            },
            grid: {
              top: 24,
              bottom: 18,
              right: 0,
            },
            xAxis: {
              type: "category",
              data: xDatas,
              boundaryGap: false,
              axisTick: { show: false },
              axisLine: { show: false },
              axisLabel: {
                fontSize: 12,
                color: "hsla(219, 52%, 81%, 0.5)",
              },
            },
            yAxis: {
              type: "value",
              show: true,
              axisTick: { show: false },
              splitLine: { show: false },
              splitNumber: 3,
              axisLabel: {
                verticalAlign: "bottom",
                fontSize: 12,
                color: "hsla(219, 52%, 81%, 0.5)",
              },
            },

            dataZoom: [{ type: "inside" }],
            series: Object.keys(chartData).map((key) => {
              return {
                name: key,
                type: "line",
                smooth: true,
                showSymbol: false,
                itemStyle: { color: this.concretePumpPerformanceColorMap[key] },
                data: chartData[key].chartData,
              };
            }),
          };

          myChart.setOption(option);
          myChart.on("legendselectchanged", this.onLegendSelectChanged);
          this.chartInstances.push(myChart);
        }
      },

      //添加hint二维标签
      addHintAnnotation(geographical, arg) {
        // console.log("添加hint锚点");
        const scene = window.scene;
        if (scene) {
          const annotation = scene.addFeature("annotation", arg.id);
          annotation.origin = [geographical[0], geographical[1]];
          annotation.altitude = geographical[2];
          const data = {
            position: { x: 0, y: 0, z: 0 }, //偏移量
            // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
            content: null,
            // 二维标签html，自定义html字符串
            htmlCode: `<div data-id="${arg.id}" class="relative w-[52px] h-[52px]">
                  <img data-id="${arg.id}" class="isHintIcon" src="/anchorIcons/hint.png" alt="hint"/>
                  <div id="content-${arg.id}" class="hint-contentwrapper absolute -top-full left-full ml-[2px]"></div>
                </div>`,
            // 二维标签样式设置，自定义css字符串
            // 如果二次添加的锚点样式相同，该参数可为空
            cssCode: ``,
            // 二维标签js，自定义字符串
            //增加一个window.page里面包含可以公用的方法
            jsCode: `annotation.addEventListener("click",function(e){
              const target = e?.target??null;
              if(target){
                const targetClassList = target.classList;
                if(targetClassList.contains("hint")||targetClassList.contains("isHintIcon")){
                  window.page?.onHintPointClick(e.target.dataset.id);
                }else if(targetClassList.contains("isClose")){
                  window.page?.resetAllHintPoint();
                }
              }
              });`,
            // 自定义锚点类型，默认为default,自定义标签为custom
            setType: { anchorType: "custom" },
          };
          // 二维标签数据标识
          annotation.dataKey = "annotation-" + annotation.id;
          // 二维标签数据设置
          annotation.data = { position: { x: 0, y: 0, z: 0 } };
          // 设置标签数据
          scene.postData(data, "annotation-" + annotation.id);
          // 加载二维标签
          annotation.load();
        }
      },

      //移除hint二维标签
      removeHintAnnotation() {
        // console.log("移除hint锚点");
        if (window.scene) {
          const reg = /^\s*hint-/i;
          const scene = window.scene;
          window.scene.features.forEach((feature) => {
            if (reg.test(feature?.id + "")) {
              scene.removeFeature(feature.id, true);
            }
          });
        }
      },

      //重置所有hint二维标签
      resetAllHintPoint() {
        const targets = document.querySelectorAll(".hint-contentwrapper");
        if (targets?.length) {
          targets.forEach((item) => {
            item.innerHTML = "";
          });
        }
        this.hintPontId = "";
      },

      //响应hint锚点点击事件
      onHintPointClick(id) {
        if (this.hintPontId === id) return;
        this.hintPontId = id;
        this.fit2Feature(id);
        const targetDom = document.getElementById(`content-${id}`);
        if (targetDom) {
          targetDom.innerHTML = dirtStatusTemplate;
        }
      },

      //将目标元素聚焦到画布中央
      fit2Feature(id) {
        const scene = window.scene;
        if (scene) {
          const target = scene.findFeature(id);
          target && scene.fit2Feature(target, 1, true);
        }
      },

      //暴露接口到全局环境
      exposeToWindow() {
        window.page = this;
      },

      //移除暴露的接口
      deleteExposeToWindow() {
        window.page = null;
      },

      //隐藏车模型
      hideCar() {
        if (window.scene) {
          const feature = window.scene.findFeature("140ee19a-2299-a8c2-955c-e12b7f60a4dd");
          feature && (feature.visible = false);
        }
      },

      //显示车模型
      showCar() {
        if (window.scene) {
          const feature = window.scene.findFeature("140ee19a-2299-a8c2-955c-e12b7f60a4dd");
          feature && (feature.visible = true);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .viewMainBG {
    background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9), hsla(236, 33%, 18%, 0.9)) center / contain no-repeat border-box;
  }
  .titleBG {
    background: linear-gradient(90deg, hsla(212, 26%, 50%, 0.4), hsla(211, 17%, 39%, 0.4), hsla(225, 11%, 7%, 0.4)) left center/contain no-repeat border-box;
  }
</style>
