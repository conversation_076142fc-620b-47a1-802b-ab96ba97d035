<template>
  <div class="viewWrapper">
    <!--当日天气-->
    <div class="absolute z-50 w-[876px] h-[64px] top-32 left-[50%] -translate-x-[50%]">
      <div class="text-base mb-2">工地今日天气情况</div>
      <div class="todayWeatherBG flex items-center justify-evenly w-[876px] h-[64px]">
        <div class="flex items-center">
          <img class="w-[36px] h-[36px] mr-3" src="/images/env/qing-icon.png" alt="天气" />
          <div>
            <div class="text-lg">{{ weatherInfo?.weather }}</div>
            <div class="text-[12px]">天气</div>
          </div>
        </div>
        <div class="flex items-center">
          <img class="w-[36px] h-[36px] mr-3" src="/images/env/temputer.png" alt="温度" />
          <div>
            <div class="text-lg">{{ weatherInfo?.temp }}&nbsp;°C</div>
            <div class="text-[12px]">温度</div>
          </div>
        </div>
        <div class="flex items-center">
          <img class="w-[36px] h-[36px] mr-3" src="/images/env/wet.png" alt="湿度" />
          <div>
            <div class="text-lg">{{ weatherInfo?.humidity }}&nbsp;</div>
            <div class="text-[12px]">湿度</div>
          </div>
        </div>
        <div class="flex items-center">
          <img class="w-[36px] h-[36px] mr-3" src="/images/env/speed.png" alt="风速" />
          <div>
            <div class="text-lg">{{ weatherInfo?.wdspd }}</div>
            <div class="text-[12px]">风速</div>
          </div>
        </div>
        <div class="flex items-center">
          <img class="w-[36px] h-[36px] mr-3" src="/images/env/direction.png" alt="风向" />
          <div>
            <div class="text-lg">{{ weatherInfo?.wd }}</div>
            <div class="text-[12px]">风向</div>
          </div>
        </div>
      </div>
    </div>

    <!--24小时环境监测，根据测点类型条件显示-->
    <div v-show="is24HoursMonitorVisible" class="allDayEnvmonitorBG absolute z-50 w-[1118px] h-[230px] bottom-4 left-[50%] -translate-x-[50%] rounded-[34px]">
      <div class="w-full h-full pt-4 px-4 flag" v-loading="isLoadingAllDayEnvMonitorList">
        <!--内容组件本身-->
        <div class="relative w-full h-full">
          <!--背景-->
          <img class="absolute top-0 left-0 w-[394px] h-[30px]" src="/images/env/24h-monitor.png" alt="24小时环境监测" />
          <!--下一个-->
          <img
            class="cursor-pointer w-[24px] h-[24px] absolute right-0 top-[50%] -translate-y-[50%]"
            :class="{ 'cursor-not-allowed': isLast24 }"
            src="/images/env/tran-rigt.png"
            @click="onNextEnvMonitorClick"
            alt="下一个"
          />
          <!--上一个-->
          <img
            class="cursor-pointer w-[24px] h-[24px] absolute left-0 top-[50%] -translate-y-[50%] rotate-180"
            :class="{ 'cursor-not-allowed': isFirst24 }"
            src="/images/env/tran-rigt.png"
            @click="onPrevEnvMonitorClick"
            alt="上一个"
          />
          <!--内容区-->
          <div class="flex mx-[32px] overflow-hidden h-full pt-[48px] items-start justify-center">
            <div class="w-[50%] flex-none flex flex-col h-full" v-for="item in allDayEnvMonitorListForView" :key="item.key">
              <div class="allDayMonitorTitleBG flex items-center text-sm pl-3 w-[108px] h-[24px]">{{ item.name }} </div>
              <div :id="item.key" class="w-full h-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--右侧内容-->
    <collapse direction="right" class="absolute z-50 top-32 right-0">
      <div class="viewRightBG w-[382px] h-[920px] pt-[18px] pl-[12px] rounded-tl-[12px] rounded-bl-[12px]">
        <img class="w-[370px] h-[30px]" src="/images/env/points-monitor.png" alt="测点监测" />
        <!--环境监测、视频监控-->
        <div class="flex justify-start items-center space-x-4 mt-4">
          <div
            v-for="v in measurePointTypeTabs"
            :key="v.id"
            @click="onMeasurePointTypeTabClick(v)"
            class="pointType cursor-pointer flex items-center justify-center w-[102px] h-[34px] rounded-[4px]"
            :class="{ activated: v.id === measurePointTypeId }"
            >{{ v.label }}</div
          >
        </div>
        <!--监测列表,可滚动-->
        <div class="scrollbarCus w-full h-[480px] overflow-x-hidden overflow-y-auto mt-4" :key="measurePointTypeId" v-loading="isLoadingGroupPoints">
          <template v-if="measurePointTypeId === 'env'">
            <div v-for="group in measurePointGroups" :key="group.groupId" class="groupWrapper">
              <div class="flex justify-between items-center pr-4 mb-4">
                <div class="pointGroupBG w-[262px] h-[48px] pl-4 flex items-center"> {{ group.groupName }} </div>
              </div>
              <div
                v-for="detail in group.details"
                :key="detail.pointcode"
                class="flex justify-between items-center pl-4 pr-4 mb-3 cursor-pointer"
                @click="onEnvPointClick(detail, detail.id)"
              >
                <div class="pointDetailBG w-[262px] h-[48px] flex items-center">
                  <img class="-mt-4 w-[18px] h-[18px] self-auto" src="/images/env/level.png" alt="" />
                  <div class="ml-2">
                    <div>{{ detail?.position }}</div>
                    <div class="text-[12px] opacity-30">{{ detail?.pointcode }}</div>
                  </div>
                </div>
                <el-radio class="-mt-5 flex-1 leading-[48px] text-right" v-model="selectedPointId" :label="detail.id" />
              </div>
            </div>
          </template>
          <template v-else-if="measurePointTypeId === 'video'">
            <div v-for="group in measurePointGroups" :key="group.groupId" class="groupWrapper">
              <div class="flex justify-between items-center pr-4 mb-3">
                <div class="pointGroupBG w-[262px] h-[48px] pl-4 flex items-center"> {{ group.groupName }} </div>
              </div>
              <div
                v-for="(detail, index) in group.details"
                :key="index"
                class="flex justify-between items-center pl-4 pr-4 mb-3 cursor-pointer"
                @click="onVideoPointClick(detail, detail.id)"
              >
                <div class="pointDetailBG w-[262px] h-[32px] flex items-center">
                  <img class="w-[18px] h-[18px]" src="/anchorIcons/camera.png" alt="" />
                  <div class="ml-2">{{ detail?.videoname }}</div>
                </div>
                <el-radio class="flex-1 leading-[32px] text-right" v-model="selectedPointId" :label="detail.id" />
              </div>
            </div>
          </template>
        </div>
        <!--测点预警-->
        <div class="mt-0">
          <img class="w-[370px] h-[30px]" src="/images/env/points-warning.png" alt="测点预警" />
          <div class="pt-3">
            <timeline :items="measurePointWarningList" :warningKey="`is${interfaceKeyValueMapKey}Warning`">
              <template #start="{ item }">
                <div class="text-center font-pingfang-regular">
                  <div class="text-[12px]">{{ item.datatimeForView.format("HH:mm:ss") }}</div>
                  <div class="text-[10px] opacity-40">{{ item.datatimeForView.format("YYYY/MM/DD") }}</div>
                </div>
              </template>
              <template #end="{ item }">
                <div class="flex justify-between items-center">
                  <div class="space-y-2">
                    <div class="flex items-center">
                      <div class="mr-2 text-base font-pingfang">{{ item.pointname }}</div>
                      <div class="text-[12px] font-pingfang-regular opacity-50">{{ interfaceKeyValueMap[interfaceKeyValueMapKey].title }}</div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div class="min-w-[56px]">
                        <div class="text-[12px] font-pingfang-regular">监测值</div>
                        <div class="text-[14px] font-pingfang">
                          {{ item[interfaceKeyValueMapKey] }}&nbsp;{{ interfaceKeyValueMap[interfaceKeyValueMapKey].unit }}
                        </div>
                      </div>
                      <divide width="1px" height="16px" :opacity="0.3" />
                      <div class="min-w-[56px]">
                        <div class="text-[12px] font-pingfang-regular">上限</div>
                        <div class="text-[14px] font-pingfang">
                          &lt;=&nbsp;{{ item[interfaceKeyValueMap[interfaceKeyValueMapKey].threshold_max] }}&nbsp;{{
                            interfaceKeyValueMap[interfaceKeyValueMapKey].unit
                          }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="mr-4 w-[44px] h-[44px] flex justify-center items-center text-[14px] font-pingfang rounded-[6px]"
                    :class="{ 'bg-[#EF424E]': item[`is${interfaceKeyValueMapKey}Warning`], 'bg-[#5D6A7C]': !item[`is${interfaceKeyValueMapKey}Warning`] }"
                  >
                    {{ item[`is${interfaceKeyValueMapKey}Warning`] ? "预警" : "安全" }}
                  </div>
                </div>
              </template>
            </timeline>
          </div>
        </div>
      </div>
    </collapse>
  </div>
</template>

<script>
  import { EnvEffectAPI } from "../api/envEffect";
  import { mapGetters } from "vuex";

  //环境监测页签分组名称映射
  const GroupNameMap = { ground: "地面环境", underground: "地下环境" };

  //https://open.ys7.com/ezopen/h5/iframe_se
  //ezopen://open.ys7.com/#device_serial#/#channel#.hd.live 设备序列号 通道号
  const videoPointTemplate2 = `
      <div class="absolute top-0 left-full ml-[2px] w-[450px] h-[315px] bg-[#151515] rounded-[6px] p-[12px] cursor-auto">
        <div class="flex justify-between">
          <div class="font-pingfang">
            <div class="text-[14px]">#videoname#</div>
            <div class="text-[#fff] text-opacity-30 text-[12px]">编号:&nbsp;#device_serial#</div>
          </div>
          <img class="mt-[6px] cursor-pointer isClose w-[12px] h-[12px]" src="/images/env/close.png" alt="关闭" />
        </div>
        <iframe
          class="mt-[6px] w-full h-[244px] rounded-tl-[6px] rounded-tr-[6px]"
          src="https://open.ys7.com/ezopen/h5/iframe?url=#videourl#&autoplay=1&accessToken=#accessToken#" allowfullscreen frameborder="no">
        </iframe>
      </div>
  `;

  //环境监测锚点html结构
  const envPointTemplate = `<div class="absolute cursor-pointer z-50 top-0 left-full w-[440px] h-[206px] rounded-[12px] bg-lime-500" style="background: linear-gradient(0deg, #608944 0%, #2f5d10 100%) no-repeat left center / cover border-box;">
      <div class="flex justify-between pt-4 px-5">
        <div>#pointname#&nbsp;&nbsp;#pointcode#</div>
        <img class="cursor-pointer isClose w-[12px] h-[12px]" src="/images/env/close.png" alt="关闭" />
      </div>
      <div class="space-y-2 px-5 mt-2">
        <div class="text-[12px] opacity-30"> 监测时间:&nbsp;#datatimeForView#</div>
        <div class="grid grid-cols-3 gap-x-12 gap-y-3">
          <div class="flex items-center">
            <img class="w-[36px] h-[36px] mr-2" src="/images/env/temputer2.png" alt="温度" />
            <div>
              <div class="text-[12px]">温度</div>
              <div class="text-lg">#temperature#<span class="ml-2 text-[12px]">°C</span> </div>
            </div>
          </div>
          <div class="flex items-center">
            <img class="w-[36px] h-[36px] mr-2" src="/images/env/wet2.png" alt="湿度" />
            <div>
              <div class="text-[12px]">湿度</div>
              <div class="text-lg">#humidity#<span class="ml-2 text-[12px]">%</span> </div>
            </div>
          </div>
          <div class="flex items-center">
            <img class="w-[36px] h-[36px] mr-2" src="/images/env/noise.png" alt="温度" />
            <div>
              <div class="text-[12px]">噪音</div>
              <div class="text-lg">#noise#<span class="ml-2 text-[12px]">dB</span> </div>
            </div>
          </div>
          <div class="flex-1 flex items-center">
            <img class="w-[36px] h-[36px] mr-2" src="/images/env/pm2.5.png" alt="pm2.5" />
            <div>
              <div class="text-[12px]">PM2.5</div>
              <div class="text-lg">#pm2_5#<span class="ml-2 text-[12px]">mg/m³</span> </div>
            </div>
          </div>
          <div class="flex items-center">
            <img class="w-[36px] h-[36px] mr-2" src="/images/env/pm10.png" alt="pm10" />
            <div>
              <div class="text-[12px]">PM10</div>
              <div class="text-lg">#pm10#<span class="ml-2 text-[12px]">mg/m³</span> </div>
            </div>
          </div>
        </div>
      </div>
    </div>`;

  //视频监控锚点html结构
  // const videoPointTemplate = `
  //       <div class="absolute top-0 left-full ml-[2px] w-[450px] h-[310px] bg-[#151515] rounded-[6px] p-[12px] cursor-auto">
  //         <div class="flex justify-between">
  //           <div class="font-pingfang">
  //             <div class="text-[14px]">#videoname#</div>
  //             <div class="text-[#fff] text-opacity-30 text-[12px]">编号:&nbsp;#videocode#</div>
  //           </div>
  //           <img class="mt-[6px] cursor-pointer isClose w-[12px] h-[12px]" src="/images/env/close.png" alt="关闭" />
  //         </div>
  //         <video id="videoPlayer" style="border-top-left-radius: 10px;border-top-right-radius: 10px;" class="video-js vjs-big-play-centered mt-[6px] w-full h-[244px] rounded-tl-[10px] rounded-tr-[10px]"></video>
  //       </div>
  // `;

  export default {
    name: "EnvEffect",
    data() {
      return {
        measurePointTypeTabs: [
          {
            id: "env",
            label: "环境监测",
          },
          {
            id: "video",
            label: "视频监控",
          },
        ], //测点监测tabs
        measurePointTypeId: "env", //选中的测点监测Tab编号
        measurePointGroups: [], //测点监测组列表
        isLoadingGroupPoints: false, //测点监测组加载标志
        selectedPointId: "", //选中的测点编号、视频编号
        measurePointInfo: null, //当前选中环境测点最新的信息
        allDayEnvMonitorList: [], //24小时环境监测信息列表
        allDayRefreshInterval: 1000 * 30, //24小时环境监测刷新周期
        allDayRefreshTimer: null, //24小时环境监测timer
        isLoadingAllDayEnvMonitorList: false, //加载24小时环境监测信息标志
        allDayEnvMonitorIndex: -1, //24小时环境监测当前索引
        allDayEnvMonitorStep: 2, //24小时环境监测左右移动步长
        chartInstances: [], //echart实例数组
        vieoPlayerInstance: null, //videojs实例
        measurePointWarningList: [], //测点预警列表
        measurePointKeyRotateTimer: null, //测点预警测点key轮换timer
        measurePointKeyRotateInterval: 1000 * 2, //测点预警测点key轮换周期
        //接口返回值结构key映射
        interfaceKeyValueMap: {
          temperature: {
            title: "温度", //环境监测标题
            threshold_max: "threshold_temp_max", //环境监测阈值上限
            threshold_min: "threshold_temp_min", //环境监测阈值下限
            unit: "°C",
          },
          humidity: {
            title: "湿度", //24小时环境监测标题
            threshold_max: "threshold_damp_max", //境监测阈值上限
            threshold_min: "threshold_damp_min", //境监测阈值下限
            unit: "%",
          },
          noise: {
            title: "噪音", //环境监测标题
            threshold_max: "threshold_sound_max", //环境监测阈值上限
            threshold_min: "threshold_sound_min", //环境监测阈值下限
            unit: "dB",
          },
          pm2_5: {
            title: "PM2.5", //环境监测标题
            threshold_max: "threshold_pm25_max", //环境监测阈值上限
            threshold_min: "threshold_pm25_min", //环境监测阈值下限
            unit: "mg/m³",
          },
          pm10: {
            title: "PM10", //环境监测标题
            threshold_max: "threshold_pm10_max", //环境监测阈值上限
            threshold_min: "threshold_pm10_min", //环境监测阈值下限
            unit: "mg/m³",
          },
        },
        interfaceKeyValueMapKeyIndex: 0,
        weatherInfo: {},
      };
    },
    computed: {
      ...mapGetters("common", ["currentProjectId", "currentProjectRegionCode"]),
      is24HoursMonitorVisible() {
        return this.measurePointTypeId === "env" && this.selectedPointId;
      },
      allDayEnvMonitorListForView() {
        const { allDayEnvMonitorIndex, allDayEnvMonitorStep, allDayEnvMonitorList } = this;
        if (allDayEnvMonitorList?.length) {
          const listForView = allDayEnvMonitorList.slice(allDayEnvMonitorIndex, allDayEnvMonitorIndex + allDayEnvMonitorStep);
          if (listForView.length < allDayEnvMonitorStep) {
            listForView.push(allDayEnvMonitorList[0]);
          }
          return listForView;
        }
        return [];
      },
      isFirst24() {
        return this.allDayEnvMonitorIndex <= 0;
      },
      isLast24() {
        const { allDayEnvMonitorIndex, allDayEnvMonitorStep, allDayEnvMonitorList } = this;
        return allDayEnvMonitorIndex + allDayEnvMonitorStep >= allDayEnvMonitorList.length;
      },
      interfaceKeyValueMapKey() {
        return this.keysFoIinterfaceKeyValueMap[this.interfaceKeyValueMapKeyIndex];
      },
      keysFoIinterfaceKeyValueMap() {
        return Object.keys(this.interfaceKeyValueMap);
      },
    },
    watch: {
      currentProjectId: {
        immediate: true,
        handler(cv) {
          cv && this.updateThePage();
        },
      },
      allDayEnvMonitorIndex() {
        this.disposeTheCharts();
        this.$nextTick(() => {
          this.allDayEnvMonitorListForView.forEach((item) => {
            this.showMonitorChart(item);
          });
        });
      },
    },
    mounted() {
      this.exposeToWindow();
    },
    beforeDestroy() {
      clearTimeout(this.measurePointKeyRotateTimer);
      clearTimeout(this.allDayRefreshTimer);
      this.disposeTheCharts();
      this.disposeTheVideoPlayer();
      this.deleteExposeToWindow();
      this.removeAnchors();
    },
    methods: {
      //更新页面
      updateThePage() {
        clearTimeout(this.measurePointKeyRotateTimer);
        clearTimeout(this.allDayRefreshTimer);
        this.disposeTheCharts();
        this.disposeTheVideoPlayer();
        this.updateSelectedTab();
        this.updateMeasurePointWarningList();
        this.updateWeatherInfo();
      },
      //释放echart实例
      disposeTheCharts() {
        this.chartInstances.forEach((item) => {
          item.dispose();
        });
        this.chartInstances = [];
      },
      //释放videojs实例
      disposeTheVideoPlayer() {
        this.vieoPlayerInstance?.dispose();
        this.vieoPlayerInstance = null;
      },
      //从测点组中根据matcher获取符合matcher的元素
      getTargetFromMeasurePointGroups(matcher) {
        const stack = JSON.parse(JSON.stringify(this.measurePointGroups));
        while (stack.length) {
          const current = stack.pop();
          if (matcher(current)) {
            return current;
          }
          if (current?.details) {
            stack.push(...current.details);
          }
        }
      },
      //响应测点监测组tab点击事件
      onMeasurePointTypeTabClick(item) {
        if (item.id === this.measurePointTypeId) return;
        clearTimeout(this.allDayRefreshTimer);
        this.disposeTheCharts();
        this.disposeTheVideoPlayer();
        this.measurePointTypeId = item.id;
        this.$nextTick(this.updateSelectedTab);
      },
      //更新选中的测点监测tab页签
      updateSelectedTab() {
        //prettier-ignore
        switch (this.measurePointTypeId) {
          //todo 桃浦604和绿建楼不聚焦，t2塔楼才聚焦到锚点
          case "env": {  this.updateEnvMonitorTab(); break; }
          case "video": {  this.updateVideoMonitorTab(); break; }
        }
      },
      //更新环境监测页签
      async updateEnvMonitorTab() {
        this.isLoadingGroupPoints = true;
        this.removeAnchors();
        this.measurePointGroups = [];
        let res = await EnvEffectAPI.getProjectGroupPoints();
        res = res?.code === 0 ? res.data : {};
        delete res.underground;
        this.measurePointGroups = Object.keys(res).map((key) => {
          return {
            groupId: key,
            groupName: GroupNameMap[key],
            details: res[key].map((item) => {
              return {
                ...item,
                id: `env-${this.$nanoid()}`,
              };
            }),
          };
        });
        this.isLoadingGroupPoints = false;
        this.addAnchors();
        const point = this.measurePointGroups[0]?.details?.[0] ?? null;
        this.onEnvPointClick(null, point?.id);
        this.updateMeasurePointWarningList();
      },
      //添加环境监测锚点
      addEnvAnchors() {
        if (window.scene) {
          console.log("加入环境锚点");
          const points = this.measurePointGroups[0]?.details ?? [];
          if (points.length) {
            switch (this.currentProjectId) {
              case "42": {
                //T2塔楼附近的点
                points[0] && this.addEnvPointDataAnnotation([121.4313035297892, 31.196115771487428, 0.19999968580497704], { id: points[0].id });
                points[1] && this.addEnvPointDataAnnotation([121.43024805531684, 31.196934615884985, 0.19999795500035694], { id: points[1].id });
                points[2] && this.addEnvPointDataAnnotation([121.43039155838824, 31.195428154555913, 0.20000113920042972], { id: points[2].id });
                points[3] && this.addEnvPointDataAnnotation([121.42845094648737, 31.195389621356725, 0.20000122064605422], { id: points[3].id });
                break;
              }
              case "46": {
                //todo 更新为绿建楼附近的点
                points[0] && this.addEnvPointDataAnnotation([121.37964701653704, 31.27738240174733, 0], { id: points[0].id });
                points[1] && this.addEnvPointDataAnnotation([121.37943475285624, 31.276963050989366, 0], { id: points[1].id });
                points[2] && this.addEnvPointDataAnnotation([121.37873275062805, 31.277238459708045, 0], { id: points[2].id });
                points[3] && this.addEnvPointDataAnnotation([121.3789307961087, 31.277724610367272, 0], { id: points[3].id });
                break;
              }
              case "97": {
                //todo 更新为桃浦604项目附近的点
                points[0] && this.addEnvPointDataAnnotation([121.38500489627302, 31.2780913660294, 1.0542427162885286e-25], { id: points[0].id });
                points[1] && this.addEnvPointDataAnnotation([121.38580832839142, 31.27799788918483, 5.532636069300555e-10], { id: points[1].id });
                points[2] && this.addEnvPointDataAnnotation([121.38557102086611, 31.277352877598005, 5.532718878760585e-10], { id: points[2].id });
                points[3] && this.addEnvPointDataAnnotation([121.38535170937342, 31.276639989272056, 5.533094273212471e-10], { id: points[3].id });
                break;
              }
            }
          }
        }
      },
      //添加环境锚点二维标签
      addEnvPointDataAnnotation(geographical, arg) {
        const scene = window.scene;
        if (scene) {
          const annotation = scene.addFeature("annotation", arg.id);
          annotation.origin = [geographical[0], geographical[1]];
          annotation.altitude = geographical[2];

          const data = {
            position: { x: 0, y: 0, z: 0 }, //偏移量
            // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
            content: null,
            // 二维标签html，自定义html字符串
            htmlCode: `<div id="content-${arg.id}" class="envPointClass"></div>`,
            // 二维标签样式设置，自定义css字符串
            // 如果二次添加的锚点样式相同，该参数可为空
            cssCode: `
            .envPointClass{
                position: relative;
                z-index: 50;
                width: 36px;
                height: 36px;
                background: url("/anchorIcons/triangle2.png") no-repeat center/cover border-box;
              }
            .envPointClass.activated{
              background: url("/anchorIcons/triangle.png") no-repeat center/cover border-box;
            }`,
            // 二维标签js，自定义字符串
            //增加一个window.page里面包含可以公用的方法
            jsCode: `annotation.addEventListener("click",function(e){
            const target = e?.target??null;
            if(target){
              const targetClassList = target.classList;
              if(targetClassList.contains("envPointClass")){
                window.page?.onEnvPointClick(null,e.target.id.replace("content-",""));
              }else if(targetClassList.contains("isClose")){
                window.page?.resetAllEnvPoint();
              }
            }
          });`,
            // 自定义锚点类型，默认为default,自定义标签为custom
            setType: { anchorType: "custom" },
          };
          // 二维标签数据标识
          annotation.dataKey = "annotation-" + annotation.id;
          // 二维标签数据设置
          annotation.data = { position: { x: 0, y: 0, z: 0 } };
          // 设置标签数据
          scene.postData(data, "annotation-" + annotation.id);
          // 加载二维标签
          annotation.load();
        }
      },
      //响应环境锚点点击事件
      onEnvPointClick(detail, id) {
        if (detail || id) {
          if (detail?.id === this.selectedPointId || id === this.selectedPointId) return;
          clearTimeout(this.allDayRefreshTimer);
          this.resetAllEnvPoint();
          this.selectedPointId = id;
          this.fit2Feature(id);
          const contentDom = document.getElementById(`content-${id}`);
          if (contentDom) {
            contentDom.classList.add("activated");
            contentDom.innerHTML = `<div class="absolute z-50 top-0 left-full flex items-center justify-center w-[440px] h-[206px] rounded-[12px] bg-lime-500" style="background: linear-gradient(0deg, #608944 0%, #2f5d10 100%) no-repeat left center / cover border-box;">
            <i class="text-[32px] el-icon-loading"></i></div>`;
          }
          this.updateMeasuerPointInfo(detail, id).then(() => {
            if (contentDom) {
              const { measurePointInfo } = this;
              measurePointInfo.datatimeForView = measurePointInfo.datatime ? this.$dayjs(measurePointInfo.datatime).format("YYYY/MM/DD HH:mm:ss") : "";
              contentDom.innerHTML = Object.keys(measurePointInfo).reduce((acc, next) => {
                return acc.replace(`#${next}#`, measurePointInfo[next]);
              }, envPointTemplate);

              //提升z-index避免覆盖
              const targetDom = document.getElementById(id);
              if (targetDom) {
                const currentIndex = Number(targetDom.style.zIndex);
                targetDom.style.zIndex = currentIndex + 1;
              }
            }
          });
          this.update24HoursCharts(detail, id);
        }
      },
      //重置环境测点
      resetAllEnvPoint() {
        const targets = document.querySelectorAll(".envPointClass");
        targets.forEach((item) => {
          this.restoreOriginalZIndex(item);
          item.classList.remove("activated");
          item.innerHTML = "";
        });
        this.selectedPointId = "";
        clearTimeout(this.allDayRefreshTimer);
      },
      //根据选中的point,更新环境测点信息
      async updateMeasuerPointInfo(detail, id) {
        this.measurePointInfo = {
          pointcode: "",
          pointname: "",
          temperature: "",
          humidity: "",
          noise: "",
          pm2_5: "",
          pm10: "",
          datatime: "",
        };
        const point = detail || this.getTargetFromMeasurePointGroups((item) => item.id === id);
        if (point) {
          const res = await EnvEffectAPI.getPointEnvironmentRealData({ pointid: point.pointid });
          if (res?.code === 0 && res.data[0]) {
            this.measurePointInfo = res.data[0];
          }
        }
      },
      //根据选中的point,更新24小时环境监测图表
      async update24HoursCharts(detail, id) {
        clearTimeout(this.allDayRefreshTimer);
        this.isLoadingAllDayEnvMonitorList = true;
        this.allDayEnvMonitorIndex = -1;
        this.allDayEnvMonitorList = [];
        const point = detail || this.getTargetFromMeasurePointGroups((item) => item.id === id);
        if (point) {
          this.allDayRefreshTimer = setTimeout(() => {
            this.update24HoursCharts(detail, id);
          }, this.allDayRefreshInterval);
          let res = await EnvEffectAPI.getPointDayHourEnvironmentRealData({ pointid: point.pointid });
          res = res?.code === 0 ? res.data : []; //res.data已经排序，按照datatime升序
          this.allDayEnvMonitorList = this.transform24HoursData(res);
          this.allDayEnvMonitorIndex = 0;
        }
        this.isLoadingAllDayEnvMonitorList = false;
      },
      //转换24小时数据
      transform24HoursData(data) {
        if (Array.isArray(data) && data.length) {
          const { interfaceKeyValueMap } = this;
          const xData = data.map((item) => item.datatime);
          return Object.keys(interfaceKeyValueMap).map((key) => {
            return {
              key: key,
              name: interfaceKeyValueMap[key].title,
              xData: xData,
              seriesData: data.map((item) => item[key]),
              threshold_max_key: interfaceKeyValueMap[key].threshold_max,
              unit: interfaceKeyValueMap[key].unit,
            };
          });
        }
        return [];
      },
      //响应24小时环境监测，向前导航
      onPrevEnvMonitorClick() {
        if (this.isFirst24) return;
        this.allDayEnvMonitorIndex -= this.allDayEnvMonitorStep;
        if (this.allDayEnvMonitorIndex < 0) this.allDayEnvMonitorIndex = 0;
      },
      //响应24小时环境监测，向后导航
      onNextEnvMonitorClick() {
        if (this.isLast24) return;
        this.allDayEnvMonitorIndex += this.allDayEnvMonitorStep;
      },
      //展示24小时环境监测图表
      showMonitorChart(data) {
        const item = this.measurePointGroups[0].details.find((i) => i.id === this.selectedPointId);
        const threshold_max = item?.[data.threshold_max_key] ?? "";
        const chartDom = document.getElementById(data.key);
        if (chartDom) {
          const myChart = this.$echarts.init(chartDom);
          const that = this;
          const option = {
            grid: {
              top: 32,
              bottom: 32,
              right: 16,
            },
            xAxis: {
              type: "category",
              boundaryGap: false,
              data: data.xData,
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                formatter(value) {
                  return that.$dayjs(value).format("HH:mm");
                },
              },
            },
            yAxis: {
              type: "value",
              boundaryGap: false,
              axisLine: { show: false },
              splitLine: { show: false },
              tickLine: { show: false },
            },
            series: [
              {
                data: data.seriesData,
                type: "line",
                showSymbol: false,
                markLine: {
                  data: [{ name: "阈值上限", yAxis: threshold_max }],
                  symbol: ["none", "none"],
                  lineStyle: { type: "solid", color: "#FE5C68" },
                  label: {
                    position: "middle",
                    formatter: `警戒值: {c} ${data.unit}`,
                    color: "#FE5C68",
                    distance: 24,
                  },
                },
                areaStyle: {
                  opacity: 0.9,
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#3b3337",
                    },
                    {
                      offset: 1,
                      color: "#151624",
                    },
                  ]),
                },
                smooth: true,
                lineStyle: {
                  color: "#ffcc75",
                  width: 3,
                },
              },
            ],
          };
          myChart.setOption(option);
          this.chartInstances.push(myChart);
        }
      },
      //更新测点预警列表
      async updateMeasurePointWarningList() {
        const res = await EnvEffectAPI.getMeasurePointWarningList();
        const genKeys = this.keysFoIinterfaceKeyValueMap;
        this.measurePointWarningList = (res?.code === 0 ? res.data : []).map((item) => {
          genKeys.forEach((key) => {
            item[`is${key}Warning`] = Number(item[key]) >= Number(item[this.interfaceKeyValueMap[key].threshold_max]);
          });
          return {
            ...item,
            datatimeForView: this.$dayjs(item.datatime),
          };
        });
        setTimeout(this.rotateInterfaceKey, this.measurePointKeyRotateInterval);
      },
      //轮转当前测点的key
      rotateInterfaceKey() {
        clearTimeout(this.measurePointKeyRotateTimer);
        let currentIndex = this.interfaceKeyValueMapKeyIndex;
        if (++currentIndex >= this.keysFoIinterfaceKeyValueMap.length) {
          currentIndex = 0;
        }
        this.interfaceKeyValueMapKeyIndex = currentIndex;

        this.measurePointKeyRotateTimer = setTimeout(this.rotateInterfaceKey, this.measurePointKeyRotateInterval);
      },

      //更新视频监控页签
      async updateVideoMonitorTab() {
        this.isLoadingGroupPoints = true;
        this.removeAnchors();
        this.measurePointGroups = [];
        const res = await EnvEffectAPI.getVideoList();
        this.measurePointGroups = (res?.code === 0 ? res.data : []).map((item, index) => {
          return {
            groupId: index,
            groupName: item.areaname,
            details: item.videos.map((item2, index) => {
              return {
                ...item2,
                videoname: `${item.areaname}-${index + 1}#摄像头`,
                id: `video-${this.$nanoid()}`,
              };
            }),
          };
        });
        this.measurePointGroups = this.measurePointGroups.slice(0, 2); //只保留前2组
        this.isLoadingGroupPoints = false;
        this.addAnchors();
        this.onVideoPointClick(null, this.measurePointGroups[0]?.details?.[0]?.id);
      },
      //响应视频监控点击事件
      onVideoPointClick(detail, id) {
        if (detail || id) {
          if (detail?.id === this.selectedPointId || id === this.selectedPointId) return;
          this.disposeTheVideoPlayer();
          this.resetAllVideoPoint();
          this.selectedPointId = id;
          this.fit2Feature(id);
          let targetDom = document.getElementById(`content-${id}`);
          if (targetDom) {
            targetDom.classList.add("activated");
            targetDom = document.getElementById(`videoWrapper-${id}`);
            this.measurePointInfo = detail ?? this.getTargetFromMeasurePointGroups((item) => item?.id === id);
            // targetDom.innerHTML = videoPointTemplate
            //   .replace("#videoname#", this.measurePointInfo.videoname)
            //   .replace("#videocode#", this.measurePointInfo.videocode || "");
            // const that = this;
            // setTimeout(() => {
            //   this.$videojs(
            //     "videoPlayer",
            //     {
            //       autoplay: "play", // false true muted play any
            //       controls: true,
            //       loop: false,
            //       muted: true,
            //       preload: "auto", // auto metadata none
            //       fluid: true,
            //       language: "zh-CN",
            //       notSupportedMessage: "此视频暂无法播放，请稍后再试",
            //       controlBar: {
            //         remainingTimeDisplay: true,
            //         currentTimeDisplay: true,
            //         timeDivider: true,
            //         durationDisplay: true,
            //         progressControl: true,
            //         customControlSpacer: true,
            //         fullscreenToggle: true,
            //         volumePanel: {
            //           inline: false,
            //         },
            //       },
            //       liveTracker: { trackingThreshold: 5, liveTolerance: 5 },
            //     },
            //     function () {
            //       that.vieoPlayerInstance = this;
            //       this.on("error", function () {
            //         this.loadingSpinner.hide();
            //         setTimeout(() => {
            //           this.errorDisplay.show();
            //         }, 50);
            //       });
            //       this.src({ type: "application/x-mpegURL", src: that.measurePointInfo?.videourl });
            //     },
            //   );
            // });
            const { videoname, deviceserial, videourl, access_token } = this.measurePointInfo;
            targetDom.innerHTML = videoPointTemplate2
              .replace("#videoname#", videoname)
              .replace("#device_serial#", deviceserial)
              .replace("#videourl#", videourl)
              .replace("#accessToken#", access_token);

            //提升z-index避免覆盖
            targetDom = document.getElementById(id);
            if (targetDom) {
              const currentIndex = Number(targetDom.style.zIndex);
              targetDom.style.zIndex = currentIndex + 1;
            }
          }
        }
      },
      //重置视频测点
      resetAllVideoPoint() {
        this.disposeTheVideoPlayer();
        const targets = document.querySelectorAll(".videoPointClass");
        targets.forEach((item) => {
          this.restoreOriginalZIndex(item);
          item.classList.remove("activated");
          if (item.children[2]) {
            item.children[2].innerHTML = "";
          }
        });
        this.selectedPointId = "";
      },
      //添加视频监控锚点
      addVideoAnchors() {
        console.log("视频加入锚点");
        // console.log(this.measurePointGroups); //结果中包含了4 5 6 7区
        const videos = this.measurePointGroups[0]?.details ?? []; //应该读取每一个区，当前只读取第一个分组(区)
        if (videos.length) {
          videos[0] && this.addVideoDataAnnotation([121.4313035297892, 31.196115771487428, 0.19999968580497704], { id: videos[0].id });
          videos[1] && this.addVideoDataAnnotation([121.42978519500781, 31.19683209983134, 0.1999981716924312], { id: videos[1].id });
          videos[2] && this.addVideoDataAnnotation([121.43039155838824, 31.195428154555913, 0.20000113920042972], { id: videos[2].id });
          videos[3] && this.addVideoDataAnnotation([121.42906016708088, 31.19575070361188, 0.20000045744161557], { id: videos[3].id });
        }
      },
      //还原z-index
      restoreOriginalZIndex(item) {
        const target = item.parentNode;
        if (target) {
          const currentIndex = Number(target.style.zIndex);
          if (currentIndex > 9) {
            target.style.zIndex = currentIndex - 1;
          }
        }
      },
      //添加视频监控二维标签
      addVideoDataAnnotation(geographical, arg) {
        const scene = window.scene;
        if (scene) {
          const annotation = scene.addFeature("annotation", arg.id);
          annotation.origin = [geographical[0], geographical[1]];
          annotation.altitude = geographical[2];
          const data = {
            position: { x: 0, y: 0, z: 0 }, //偏移量
            // 面板数据，如无面板传空，传空的话引擎不会走设置数据的逻辑。
            content: null,
            // 二维标签html，自定义html字符串
            htmlCode: `
              <div data-id='${arg.id}' id='content-${arg.id}' class="videoPointClass relative flex items-center justify-center w-[34px] h-[34px]  border border-[#FFF] rounded-full">
                <img data-id='${arg.id}' class="isCameraIcon w-[14px] h-[14px]" src="/anchorIcons/camera.png">
                <div class="absolute top-[40px] space-y-[6px]">
                  <div class="w-[8px] h-[8px] rounded-full bg-[#FFF]"></div>
                  <div class="w-[8px] h-[8px] rounded-full bg-[#FFF]"></div>
                  <div class="w-[8px] h-[8px] rounded-full bg-[#FFF]"></div>
                  <div class="w-[8px] h-[8px] rounded-full bg-[#FFF]"></div>
                  <div class="absolute left-[50%] w-[60px] h-[60px] origin-center rounded-full border border-[rgba(255,255,255,0.1)]"
                    style="background:radial-gradient(circle farthest-corner at center center, transparent 0, transparent 8px, #FFF 9px, #FFF 9px, transparent 10px, transparent 18px, rgba(255,255,255,0.3) 19px,rgba(255,255,255,0.3) 19px,transparent 20px);
                    transform:translate3d(-50%,-36px,0) rotate3d(1,0,0,56deg)"
                  >
                  </div>
                </div>
                <div id="videoWrapper-${arg.id}"></div>
              </div>
            `,
            // 二维标签样式设置，自定义css字符串
            // 如果二次添加的锚点样式相同，该参数可为空
            cssCode: `
            .videoPointClass{background-color: #4D64E1;}
            .videoPointClass.activated{background-color: #FB5101;}`,
            // 二维标签js，自定义字符串
            //增加一个window.page里面包含可以公用的方法
            jsCode: `annotation.addEventListener("click",function(e){
            const target = e?.target??null;
            if(target){
              const targetClassList = target.classList;
              if(targetClassList.contains("videoPointClass")||targetClassList.contains("isCameraIcon")){
                window.page?.onVideoPointClick(null,e.target.dataset.id);
              }else if(targetClassList.contains("isClose")){
                window.page?.resetAllVideoPoint();
              }
            }
            });`,
            // 自定义锚点类型，默认为default,自定义标签为custom
            setType: { anchorType: "custom" },
          };
          // 二维标签数据标识
          annotation.dataKey = "annotation-" + annotation.id;
          // 二维标签数据设置
          annotation.data = { position: { x: 0, y: 0, z: 0 } };
          // 设置标签数据
          scene.postData(data, "annotation-" + annotation.id);
          // 加载二维标签
          annotation.load();
        }
      },
      //暴露接口到全局环境
      exposeToWindow() {
        window.page = this;
      },
      //移除暴露的接口
      deleteExposeToWindow() {
        window.page = null;
      },
      //加入锚点
      addAnchors() {
        //prettier-ignore
        switch (this.measurePointTypeId) {
          case "env": { this.addEnvAnchors(); break; }
          case "video": { this.addVideoAnchors(); break; }
        }
      },
      //移除所有锚点
      removeAnchors() {
        const scene = window.scene;
        if (scene) {
          console.log("移除所有锚点");
          const reg = /^\s*(?:env-|video-)/i;
          scene.features.forEach((feature) => {
            if (reg.test(feature?.id + "")) {
              scene.removeFeature(feature.id, true);
            }
          });
        }
      },
      //将目标元素聚焦到画布中央
      fit2Feature(id) {
        const scene = window.scene;
        if (scene) {
          const target = scene.findFeature(id);
          target && scene.fit2Feature(target, 1, true);
        }
      },
      //更新天气信息
      async updateWeatherInfo() {
        const res = await this.$http.get(`https://api.help.bj.cn/apis/weather/?id=${this.currentProjectRegionCode}`, {
          baseURL: "/",
          withProjectId: false,
          withToken: false,
        });
        this.weatherInfo = res?.code === 0 ? res.data : null;
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::v-deep .flag .el-loading-mask {
    @apply rounded-[34px];
  }
  ::v-deep .groupWrapper .el-radio__label {
    display: none;
  }
  .todayWeatherBG {
    background: url("/images/env/today-weather-bg.png") no-repeat left center / cover border-box;
  }
  .allDayEnvmonitorBG {
    background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9) 0%, hsla(236, 33%, 18%, 0.9) 100%) no-repeat left center / cover border-box;
  }
  .allDayMonitorTitleBG {
    background: url("/images/env/title-bg2.png") no-repeat left center / cover border-box;
  }
  .viewRightBG {
    background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9) 0%, hsla(236, 33%, 18%, 0.9) 100%);
  }
  .pointType.activated {
    background-color: #1e5fd5;
  }
  .pointGroupBG {
    background: url("/images/env/title-bg.png") no-repeat left center / cover border-box;
  }
  .scrollbarCus::-webkit-scrollbar-thumb {
    background-color: #4a6cf7;
  }
</style>
