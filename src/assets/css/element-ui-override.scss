/*覆盖element-ui自带的样式放在该文件中*/

.el-input {
  height: 34px;
}

.el-input__inner {
  padding-left: 15px;
  height: 34px;
  border-radius: 0;
  border: 0;
  background: #313847;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 600;
  color: #ffffff;
}

.el-loading-mask {
  position: absolute;
  z-index: 2000;
  background-color: rgba(0, 0, 0, 0.7);
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transition: opacity 0.3s;
}

.el-loading-spinner .circular {
  margin: auto;
}

.el-table {
  background-color: #000a1f;

  &::before {
    background-color: unset;
  }

  .el-table__header {
    thead {
      color: #fff;

      tr {
        border-bottom: unset;

        th.el-table__cell.is-leaf {
          border-bottom: unset;
        }

        th {
          font-weight: 600;
        }

        .el-table__cell {
          padding: 6px 0;
          background-color: #5e616e;
        }
      }
    }
  }
  
  .el-table__body {
    tbody {
      tr.el-table__row {
        background-color: #000a1f;
        color: #fff;
        font-weight: 600;
        td.el-table__cell {
          padding: 0;
          border-bottom: unset;
        }
        &:hover {
          td.el-table__cell {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }
  }

  // &--striped {
  //   .el-table__body {
  //     tr.el-table__row--striped {
  //       td.el-table__cell {
  //         background-color: rgba(255, 255, 255, 0.1);
  //       }
  //     }
  //   }
  // }
}

.el-pagination {
  &.is-background {
    .el-pager li {
      background-color: rgba(233, 234, 240, 0.1);
      color: #fefefe;
      &:not(.disabled).active {
        background-color: #3661eb;
      }
    }

    .btn-prev {
      background-color: #3661eb;
      color: #fff;
      &:disabled {
        background-color: rgba(54, 97, 235, 0.5);
        color: rgba(246, 247, 250, 0.5);
      }
    }

    .btn-next {
      background-color: #3661eb;
      color: #f6f7fa;
      &:disabled {
        background-color: rgba(54, 97, 235, 0.5);
        color: rgba(246, 247, 250, 0.5);
      }
    }
  }
}
