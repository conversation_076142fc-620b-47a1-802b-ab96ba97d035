/*自定义全局样式放在该文件中*/

/*自定义滚动条*/
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #4a6cf7;
}

::-webkit-scrollbar-track {
  background-color: hsla(0, 0%, 100%, 0.1);
}

.scrollbar-white::-webkit-scrollbar-thumb {
  background-color: #fff;
}

body {
  overflow: hidden;
}

.vjs-control-bar {
  background-color: rgba(0, 0, 0, 0.6);
}

//覆盖videojs默认的一些样式
.vjs-slider-horizontal {
  .vjs-volume-level::before {
    top: -4px;
  }
  .vjs-play-progress::before {
    top: -5px;
  }
}

.vjs-slider-vertical .vjs-volume-level:before {
  left: -4px;
}
