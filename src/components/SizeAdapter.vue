<template>
  <div id="sizeAdapterWrapper">
    <slot></slot>
  </div>
</template>

<script>
  export default {
    name: "SizeAdapter",
    props: {
      originDeviceInfo: {
        type: Object,
        default: () => {
          return {
            width: window.innerWidth,
            height: window.innerHeight,
          };
        },
      },
    },
    created() {
      window.addEventListener("resize", this.adapteSlot);
    },
    mounted() {
      //   this.adapteSlot();
    },
    beforeDestroy() {
      window.removeEventListener("resize", this.adapteSlot);
    },
    methods: {
      adapteSlot() {
        const nowWidth = window.innerWidth;
        const nowHeight = window.innerHeight;

        const { width, height } = this.originDeviceInfo;

        const xScale = nowWidth / width;
        const yScale = nowHeight / height;
        this.$scopedSlots.default()[0].elm.style.transform = `scale3d(${xScale},${yScale},1)`;
      },
    },
  };
</script>

<style lang="scss" scoped></style>
