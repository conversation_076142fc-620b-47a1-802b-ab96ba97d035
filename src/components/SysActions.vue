<template>
  <div class="sysActionsWrapper flex items-center h-full space-x-[28px]">
    <div class="flex h-full items-center hover:cursor-pointer" @click="onNotificationClick">
      <div class="relative">
        <img class="mr-[16px] w-[20px] h-[24px]" src="../assets/images/ring.png" alt="通知" />
        <div class="absolute flex flex-wrap justify-center rounded-half items-center bg-[#FF4A34]" :style="getNotificationStyle">
          <span class="h-full inline-flex items-center" v-for="(item, index) in notificationChartList" :key="index">{{ item }}</span>
        </div>
      </div>
      <div class="text-lg opacity-60">通知</div>
    </div>
    <!-- <div class="flex items-center hover:cursor-pointer">
      <img class="mr-[12px] w-[24px] h-[24px]" src="../assets/images/setting.png" alt="设置" />
      <div class="text-lg opacity-60">设置</div>
    </div> -->
    <div class="flex h-full items-center relative hover:cursor-pointer action-mine">
      <img class="mr-[12px] w-[20px] h-[24px]" src="../assets/images/user.png" alt="我的" />
      <div class="text-[#fff] text-lg opacity-60">我的</div>
      <div class="user-profile">
        <ul class="details">
          <li class="details-item">
            <div class="label">姓名</div>
            <div class="value">{{ userProfile?.name }}</div>
          </li>
          <li class="details-item">
            <div class="label">电话</div>
            <div class="value">{{ userProfile?.mobile }}</div>
          </li>
          <li class="details-item">
            <div class="label">邮箱</div>
            <div class="value">{{ userProfile?.email }}</div>
          </li>
          <li class="details-item">
            <div class="label">工号</div>
            <div class="value">{{ userProfile?.worknumber }}</div>
          </li>
          <li class="details-item">
            <div class="label">部门</div>
            <div class="value">{{ userProfile?.department_name }}</div>
          </li>
        </ul>
      </div>
    </div>
    <div class="flex h-full items-center hover:cursor-pointer" @click.stop="doLogout">
      <img class="mr-[12px] w-[20px] h-[16px]" src="../assets/images/quit.png" alt="退出" />
      <div class="text-lg opacity-60">退出</div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  import { toLoginPage } from "../utils/logout";
  import { getUserProfile } from "../utils/getUserProfile";
  export default {
    name: "SysActions",
    data() {
      return {
        userProfile: null,
      };
    },
    computed: {
      ...mapGetters("common", ["notificationCount"]),
      notificationChartList() {
        return ("" + this.notificationCount).split("");
      },
      getNotificationStyle() {
        if (this.notificationChartList.length < 2) {
          return {
            width: "18px",
            height: "18px",
            top: "-12px",
            right: "6px",
          };
        } else {
          return {
            width: "22px",
            height: "22px",
            top: "-12px",
            right: 0,
          };
        }
      },
    },
    mounted() {
      this.userProfile = getUserProfile() ?? null;
    },
    methods: {
      onNotificationClick() {
        this.$store.commit("common/setArgForGetRiskData", { page: 1, limit: 10, name: "", status: -1, warningType: "", upgradeType: "", riskType: "" });
        this.$store.commit("common/setIsNotificationListVisible", true);
      },
      doLogout() {
        toLoginPage();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-mine:hover {
    .user-profile {
      display: block;
    }
  }
  .user-profile {
    display: none;
    position: absolute;
    z-index: 2;
    width: 268px;
    top: calc(50% + 20px);
    left: 50%;
    transform: translate3d(-50%, 0, 0);
    .details {
      padding: 12px;
      font-size: 16px;
      color: #ddd;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
      background-color: rgba(0, 10, 30, 0.9);
      &-item {
        display: flex;
        margin-top: 12px;
        height: 32px;
        line-height: 32px;
        padding: 0 12px;
        align-items: center;
        &:hover {
          background-color: rgba(94, 97, 110, 0.5);
        }
        .label {
          width: 32px;
          flex: 0 1 auto;
        }
        .value {
          padding-left: 50px;
          flex: 1 1 auto;
        }
      }
    }
  }
</style>
