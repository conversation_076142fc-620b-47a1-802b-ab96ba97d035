<template>
  <div class="projectInfoWrapper relative min-w-[863px] inline-block">
    <img class="absolute bottom-6 left-2 max-h-52" :class="{ 'opacity-0': isLoading }" src="../assets/images/home/<USER>" alt="建筑物" />
    <div class="h-28 p-6 pr-8 pl-32 rounded-tl-[16px] rounded-tr-[16px] bg-[#334caf]">
      <div class="mb-2 text-2xl font-bold">{{ currentProjectLabel }}</div>
      <div class="flex items-center space-x-6">
        <div class="flex-1 flex items-center space-x-6">
          <div class="font-bold text-base">已安全无事故&nbsp;&nbsp;(天)</div>
          <div class="flex items-center space-x-3 font-ding">
            <div v-for="(num, i) in safeDays" :key="i" class="px-3 py-1 rounded-md text-2xl bg-[#485eb7]">
              {{ num }}
            </div>
          </div>
        </div>
        <divide width="1px" height="20px" bgc="#fff" :opacity="0.3" />
        <div class="flex-1 flex items-center justify-between">
          <div class="flex items-center">
            <img v-if="environmentInfo?.weatherimg" class="mr-2 w-[29px] h-[24px]" :src="environmentInfo.weatherimg" alt="天气图标" />
            <div class="text-[18px] font-ding">
              {{ environmentInfo?.temp ?? "" }}
            </div>
            <div class="ml-1 text-base">°C</div>
          </div>
          <div class="flex items-center">
            <img class="mr-2 w-6 h-4" src="../assets/images/home/<USER>" alt="" />
            <div class="text-base">
              {{ environmentInfo?.wd ?? "" }}
            </div>
          </div>
          <div class="flex items-center">
            <img class="mr-2 w-4 h-5" src="../assets/images/home/<USER>" alt="" />
            <div class="text-base"> {{ environmentInfo?.humidity ?? "" }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="h-24 p-4 pr-8 pl-32 rounded-bl-[16px] rounded-br-[16px] bg-[hsla(243,21%,21%,0.9)]">
      <div class="w-1/2 inline-flex items-center space-x-6">
        <img class="w-[40px] h-[42px]" src="../assets/images/home/<USER>" alt="建筑信息" />
        <divide class="flex-none" width="1px" height="32px" bgc="#fff" :opacity="0.3" />
        <div class="-mb-2 flex flex-wrap">
          <div class="mb-2 w-1/2 flex space-x-4">
            <div class="text-base">高度:</div>
            <div class="text-base">{{ projectInfo?.height ?? "" }}米</div>
          </div>
          <div class="mb-2 w-1/2 flex space-x-4">
            <div class="text-base">层数:</div>
            <div class="text-base"> {{ projectInfo?.floorCount ?? "" }}层 </div>
          </div>
          <div class="mb-2 flex space-x-4">
            <div class="text-base">面积:</div>
            <div class="text-base">{{ projectInfo?.area ?? "" }}万方</div>
          </div>
        </div>
      </div>

      <div class="w-1/2 inline-flex items-center space-x-6">
        <img class="w-[40px] h-[42px]" src="../assets/images/home/<USER>" alt="当前进度" />
        <divide class="flex-none" width="1px" height="32px" bgc="#fff" :opacity="0.3" />
        <div class="w-full space-y-2">
          <div class="flex items-center space-x-4 text-base">
            <div>层数</div>
            <div class="flex-1 flex relative">
              <div class="w-full h-[10px] rounded-md bg-[hsla(214,33%,22%,0.1)] border"></div>
              <div
                class="absolute top-0 left-0 h-[10px] rounded-md bg-[hsla(36,97%,72%,1)]"
                :style="{
                  width: Number(projectInfo?.buildFloorRadio ?? '') * 100 + '%',
                }"
              ></div>
              <div
                class="absolute top-[50%] left-[50%] px-2 h-[22px] rounded-[10px] -translate-x-[50%] -translate-y-[50%] flex items-center justify-center text-[hsla(36,97%,72%,1)] bg-[hsla(231,15%,44%,1)] border border-[hsla(36,97%,72%,1)]"
              >
                {{ projectInfo?.buildFloor ?? "" }}层
              </div>
            </div>
            <div> {{ Number(projectInfo?.buildFloorRadio ?? "") * 100 }}% </div>
          </div>
          <div class="flex items-center space-x-4 text-base">
            <div>高度</div>
            <div class="flex-1 flex relative">
              <div class="w-full h-[10px] rounded-md bg-[hsla(214,33%,22%,0.1)] border"></div>
              <div
                class="absolute top-0 left-0 h-[10px] rounded-md bg-[hsla(36,97%,72%,1)]"
                :style="{
                  width: Number(projectInfo?.buildHeightRadio ?? '') * 100 + '%',
                }"
              ></div>
              <div
                class="absolute top-[50%] left-[50%] px-2 h-[22px] rounded-[10px] -translate-x-[50%] -translate-y-[50%] flex items-center justify-center text-[hsla(36,97%,72%,1)] bg-[hsla(231,15%,44%,1)] border border-[hsla(36,97%,72%,1)]"
              >
                {{ projectInfo?.buildHeight ?? "" }}米
              </div>
            </div>
            <div> {{ Number(projectInfo?.buildHeightRadio ?? "") * 100 }}% </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "ProjectInfo",
    props: {
      projectInfo: {
        type: Object,
        default() {
          return null;
        },
      },
      environmentInfo: {
        type: Object,
        default() {
          return null;
        },
      },
      isLoading: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      safeDays() {
        return (this.projectInfo?.safeDays ?? "") + "" ?? "";
      },
      currentProjectLabel() {
        return this.$store.getters["common/currentProject"]?.label ?? "";
      },
    },
  };
</script>
