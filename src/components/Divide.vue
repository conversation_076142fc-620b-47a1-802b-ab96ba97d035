<template>
  <div :style="styleObj"></div>
</template>

<script>
  const Directions = ["x", "y"];
  export default {
    name: "Divide",
    props: {
      direction: {
        type: String,
        default: "y",
        validator(v) {
          return Directions.includes(v);
        },
      },
      width: {
        type: String,
        default: "1px",
      },
      height: {
        type: String,
        default: "24px",
      },
      bgc: {
        type: String,
        default: "#fff",
      },
      opacity: {
        type: Number,
        default: 1,
      },
    },
    computed: {
      styleObj() {
        return {
          width: this.width,
          height: this.height,
          backgroundColor: this.bgc,
          opacity: this.opacity,
          transform: this.direction === "x" ? "rotate3d(0,0,1,90deg)" : "",
        };
      },
    },
  };
</script>
