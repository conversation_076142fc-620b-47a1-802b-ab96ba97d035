<template>
  <li class="relative m-0 list-none">
    <div class="start">
      <slot name="start" :item="data"></slot>
    </div>
    <div class="head"></div>
    <div class="tail"></div>
    <div class="end">
      <slot name="end" :item="data"></slot>
    </div>
  </li>
</template>

<script>
  export default {
    name: "TimeLineItem",
    props: {
      data: {
        type: Object,
        default() {
          return {};
        },
      },
    },
  };
</script>

<style lang="scss" scoped>
  $nodeSize: 1.5rem;
  $lineSize: 1px;
  $gap: 0.5rem;
  .start {
    position: absolute;
    width: calc(50% - $nodeSize/2 - $gap);
    width: 30px;
    max-height: calc(100% - $nodeSize/2);
    inset-inline-end: calc(50% + $gap + $nodeSize/2);
    text-align: end;
    word-break: break-all;
    // overflow-y: auto;
    // overflow-x: hidden;
  }
  .head {
    position: absolute;
    width: $nodeSize;
    height: $nodeSize;
    inset-inline-start: calc(50% - $nodeSize/2);
    border-radius: 50%;
    background: radial-gradient(circle calc($nodeSize / 3) at 50% 50%, #fff, #fff 50%, hsla(240, 5%, 40%, 0.2) calc(50% + 1px), hsla(240, 5%, 40%, 0.2) 100%)
      no-repeat center/cover border-box;
  }
  .tail {
    position: absolute;
    height: calc(100% - $nodeSize);
    inset-block-start: $nodeSize;
    inset-inline-start: calc(50% - $lineSize/2);
    border-inline-start: $lineSize solid hsla(0, 0%, 100%, 0.2);
  }
  .end {
    position: relative;
    // width: calc(50% - ($nodeSize/2) - $gap);
    width: calc(100%);
    inset-inline-start: calc(50% + ($nodeSize/2) + $gap);
    text-align: start;
    word-break: break-all;
  }
</style>
