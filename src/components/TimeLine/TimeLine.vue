<template>
  <ul class="w-full h-full">
    <li v-for="(v, i) in items" :key="i" class="relative m-0 list-none">
      <div class="py-2" :class="{ itemCenter: itemCenter }">
        <div class="start">
          <slot name="start" :item="v"></slot>
        </div>
        <!-- <template v-if="useDefaultHeadStyle"> -->
        <div class="headFixedStyle" :class="{ defaultHead: !v[warningKey], warningHead: v[warningKey] }"></div>
        <!-- </template> -->
        <!-- <template v-else>
          <div class="headFixedStyle">
            <slot name="head" :item="v"></slot>
          </div>
        </template> -->
        <div class="tail"></div>
        <div class="end">
          <slot name="end" :item="v"> </slot>
        </div>
      </div>
    </li>
  </ul>
</template>

<script>
  export default {
    name: "TimeLine",
    props: {
      items: {
        type: Array,
        default() {
          return [];
        },
      },
      itemCenter: {
        type: Boolean,
        default: true,
      },
      warningKey: {
        type: String,
        default: "isWarning",
      },
      // useDefaultHeadStyle: {
      //   type: Boolean,
      //   default: true,
      // },
    },
  };
</script>

<style lang="scss" scoped>
  $nodeSize: 1.5rem;
  $lineSize: 1px;
  $gap: 0.5rem;
  $startWidth: 70px;
  .itemCenter {
    display: flex;
    align-items: center;
  }
  .start {
    position: absolute;
    width: $startWidth;
    word-break: break-all;
  }
  .defaultHead {
    border-radius: 50%;
    background: radial-gradient(circle calc($nodeSize / 3) at 50% 50%, #fff, #fff 50%, hsla(240, 5%, 40%, 0.2) calc(50% + 1px), hsla(240, 5%, 40%, 0.2) 100%)
      no-repeat center/cover border-box;
  }
  .defaultHead {
    border-radius: 50%;
    background: radial-gradient(circle calc($nodeSize / 3) at 50% 50%, #fff, #fff 50%, hsla(240, 5%, 40%, 0.2) calc(50% + 1px), hsla(240, 5%, 40%, 0.2) 100%)
      no-repeat center/cover border-box;
  }
  .warningHead {
    border-radius: 50%;
    background: radial-gradient(circle calc($nodeSize / 3) at 50% 50%, #ef424e, #ef424e 50%, #552331 calc(50% + 1px), #552331 100%) no-repeat center/cover
      border-box;
  }
  .headFixedStyle {
    position: absolute;
    width: $nodeSize;
    height: $nodeSize;
    inset-inline-start: calc($startWidth + $gap);
    text-align: center;
    overflow: hidden;
  }
  .tail {
    position: absolute;
    height: 100%;
    inset-inline-start: calc($startWidth + $gap + $nodeSize/2 - $lineSize/2);
    border-inline-start: $lineSize solid hsla(0, 0%, 100%, 0.2);
  }
  .end {
    position: relative;
    inset-inline-start: calc($startWidth + $gap + $nodeSize + $gap);
    width: calc(100% - ($startWidth + $gap + $nodeSize + $gap));
    text-align: start;
    word-break: break-all;
  }
</style>
