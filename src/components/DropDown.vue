<template>
  <div class="dropdownWrapper relative w-[372px] h-[40px] p-[20px] flex rounded-tl-[6px] rounded-tr-[6px] bg-[#2849CD] items-center">
    <img v-if="withIcon" class="mr-[14px] w-[15px] h-[12px]" src="../assets/images/list.png" alt="列表" />
    <div class="flex items-center text-[16px] hover:cursor-pointer" @click="toggleOptions">
      {{ selectedOption?.label ?? "暂无数据" }}
    </div>
    <div class="flex-1 flex items-center justify-end hover:cursor-pointer" @click="toggleOptions">
      <div v-if="withText" class="mr-[8px] ml-[8px] text-[14px]">切换</div>
      <img class="w-[8px] h-[8px]" :class="{ 'rotate-180': isOptionVisible }" src="../assets/images/triangle-down.png" alt="下拉" />
    </div>
    <!--下拉选项-->
    <div
      class="absolute z-50 top-full right-0 left-0 pl-[16px] pr-[12px] py-[16px] rounded-bl-[6px] rounded-br-[6px] transition-transform origin-top bg-[#2849CD]"
      :class="dynamicClassStr"
    >
      <div class="max-h-[256px] space-y-2 overflow-y-auto scrollbar-white">
        <template v-if="options?.length">
          <div
            v-for="v in options"
            :key="v.id"
            class="optionItem mr-[6px] min-h-[40px] py-[8px] px-[12px] rounded-[6px] flex items-center text-base hover:bg-[hsla(0,0%,100%,0.1)] hover:cursor-pointer"
            :class="{ activated: v.id === selectedId }"
          >
            <div v-if="!v.disabled" class="w-full h-full flex justify-between items-center" @click="onOptionClick(v)">
              <div>{{ v.label }}</div>
              <template v-if="withCheck">
                <div v-show="v.id === selectedId">✓</div>
              </template>
            </div>
            <div v-else class="w-full h-full flex justify-between items-center cursor-not-allowed text-gray-400">
              <div>{{ v.label }}</div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="text-sm text-gray-400 font-thin cursor-not-allowed"> 暂无数据 </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "DropDown",
    props: {
      options: {
        type: Array,
        default() {
          return [];
        },
      },
      selectedId: {
        type: String,
        default: "",
      },
      withIcon: {
        type: Boolean,
        default: true,
      },
      withText: {
        type: Boolean,
        default: true,
      },
      withCheck: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        isOptionVisible: false,
      };
    },
    computed: {
      dynamicClassStr() {
        return this.isOptionVisible ? "scale-y-1" : "scale-y-0";
      },
      selectedOption() {
        return this.options.find((item) => item.id === this.selectedId);
      },
    },
    methods: {
      toggleOptions() {
        this.isOptionVisible = !this.isOptionVisible;
      },
      onOptionClick(item) {
        if (item.id === this.selectedId) {
          this.toggleOptions();
          return;
        }
        this.toggleOptions();
        this.$emit("change", item);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dropdownWrapper {
    box-shadow: -1px 0px 25px 0px rgba(14, 15, 15, 0.1);
  }
  .optionItem {
    &.activated {
      background-color: #0e1120;
    }
  }
</style>
