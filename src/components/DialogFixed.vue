<template>
  <el-dialog v-bind="finalAttrs" v-on="$listeners" custom-class="dialogCus" :append-to-body="false" :modal-append-to-body="false">
    <div :style="contentWrapperStyle" v-bind="$props">
      <img class="absolute top-0 bottom-0 left-0 right-0 w-full h-full" src="../assets/images/dialog/dialog-bg.png" alt="背景" />
      <img class="z-[9999]" :style="closeIconStyle" src="../assets/images/dialog/close.png" alt="关闭" @click="onCloseIconClick" />
      <div class="relative z-50 w-full h-full">
        <slot></slot>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    name: "DialogFixed",
    inheritAttrs: false,
    props: {
      width: {
        type: String,
        default: "1025px",
      },
      height: {
        type: String,
        default: "560px",
      },
      closeIconWidth: {
        type: String,
        default: "64px",
      },
      closeIconHeight: {
        type: String,
        default: "26px",
      },
      closeIconTop: {
        type: String,
        default: "17px",
      },
      closeIconRight: {
        type: String,
        default: "8px",
      },
    },
    computed: {
      contentWrapperStyle() {
        return {
          position: "relative",
          display: "flex",
          width: `${this.width}`,
          height: `${this.height}`,
          "justify-content": "center",
          "align-items": "center",
          color: "#FFF",
        };
      },
      closeIconStyle() {
        return {
          position: "absolute",
          top: `${this.closeIconTop}`,
          right: `${this.closeIconRight}`,
          width: `${this.closeIconWidth}`,
          height: `${this.closeIconHeight}`,
          cursor: "pointer",
        };
      },
      finalAttrs() {
        return Object.assign(
          {
            modal: true,
            "modal-append-to-body": true,
            "append-to-body": false,
            "lock-scroll": true,
            "close-on-click-modal": false,
            "close-on-press-escape": true,
            "show-close": false,
            "destroy-on-close": false,
          },
          this.$attrs,
        );
      },
    },
    methods: {
      onCloseIconClick() {
        this.$emit("update:visible", false);
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog.dialogCus {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: 0 !important;
    width: auto;
    background: transparent;
    border-radius: initial;
    box-shadow: initial;
    box-sizing: border-box;
    transform: translate3d(-50%, -50%, 0);
    .el-dialog__header {
      display: none;
    }
    // .el-table tr {
    //   background-color: initial;
    // }
  }
</style>
