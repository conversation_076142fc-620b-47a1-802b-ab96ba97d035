<template>
  <div class="inline-block outerWrapper" ref="outerWrapper" :style="outerWrapperStyle">
    <div class="relative collapseWrapper" ref="collapseWrapper">
      <slot ref="content"></slot>
      <div class="absolute flex justify-center items-center cursor-pointer" :class="collapseBarClass" :style="getStyleObj()" ref="bar" @click="toggleCollapsed">
        <img ref="triangleIcon" class="triangleIcon w-1.5 h-1.5" :class="triangleClass" src="../assets/images/triangle-left.png" alt="方向三角" />
      </div>
    </div>
  </div>
</template>

<script>
  const DirectionsMap = {
    left: "cleft",
    right: "cright",
    top: "ctop",
    bottom: "cbottom",
  };
  export default {
    name: "Collapse",
    props: {
      direction: {
        type: String,
        default: DirectionsMap.left,
        validator(v) {
          return Object.keys(DirectionsMap).includes(v);
        },
      },
      bgc: {
        type: String,
        default: "#334CAF",
      },
    },
    data() {
      return {
        isCollapsed: false,
        width: "",
        height: "",
      };
    },
    computed: {
      triangleClass() {
        switch (this.direction) {
          case "left":
            return "rotate-0";
          case "right":
            return "rotate-180";
          case "top":
            return "rotate-90";
          case "bottom":
            return "-rotate-90";
          default:
            break;
        }
        return "";
      },
      collapseBarClass() {
        switch (this.direction) {
          case "left":
            return "top-[50%] right-0 translate-x-full -translate-y-[50%] w-3 h-16";
          case "right":
            return "top-[50%] left-0 -translate-x-full -translate-y-[50%] w-3 h-16";
          case "top":
            return "bottom-0 left-[50%] -translate-x-[50%] translate-y-full w-16 h-3";
          case "bottom":
            return "top-0 left-[50%] -translate-x-[50%] -translate-y-full w-16 h-3";
          default:
            break;
        }
        return "";
      },
      outerWrapperStyle() {
        return {
          width: this.width,
          height: this.height,
        };
      },
    },
    mounted() {
      // console.log("Collapsed-mounted");
      this.setSizeInfo();
    },
    methods: {
      getStyleObj() {
        return {
          "background-color": this.bgc,
        };
      },
      setSizeInfo() {
        const elm = this.$scopedSlots.default()[0].elm;
        if (elm) {
          const { width, height } = window.getComputedStyle(elm);
          this.width = width;
          this.height = height;
        }
      },
      toggleCollapsed() {
        this.isCollapsed = !this.isCollapsed;
        const outerWrapperClasslist = this.$refs.outerWrapper.classList;
        if (this.isCollapsed) {
          outerWrapperClasslist.add("collapsed");
          outerWrapperClasslist.add(this.direction);
          this.$emit(`collapse${this.direction}`);
        } else {
          outerWrapperClasslist.remove("collapsed");
          outerWrapperClasslist.remove(this.direction);
          this.$emit(`uncollapse${this.direction}`);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .outerWrapper {
    transition: all 0.3s linear;
    &.collapsed {
      &.right {
        // width: 0px !important;
        transform: translate3d(100%, 0, 0);
        .triangleIcon {
          transform: rotate3d(0, 0, 1, 0deg);
        }
      }
      &.left {
        transform: translate3d(-100%, 0, 0);
        .triangleIcon {
          transform: rotate3d(0, 0, 1, 180deg);
        }
      }
      &.bottom {
        height: 0 !important;
        bottom: 0 !important;
        .triangleIcon {
          transform: rotate3d(0, 0, 1, 90deg);
        }
      }
      &.top {
        height: 0 !important;
        top: 0 !important;
        .triangleIcon {
          transform: rotate3d(0, 0, 1, 270deg);
        }
      }
    }
  }
</style>
