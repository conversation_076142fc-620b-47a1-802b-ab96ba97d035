<template>
  <span class="inline-flex justify-center items-center rounded-md h-[42px] space-x-1 bg-[hsla(0,0%,0%,0.9)] font-pingfang-regular">
    <span
      v-for="item in mapsComputed"
      :key="item.id"
      class="map flex items-center px-[16px] h-full rounded-md hover:bg-[#334CAF] hover:cursor-pointer"
      :class="{ activated: item.id === selected }"
      @click="onItemClick(item)"
    >
      {{ item.title }}
    </span>
  </span>
</template>

<script>
  export default {
    name: "SwitchMap",
    props: {
      mapId: {
        type: String,
        default: "tianditu",
      },
    },
    data() {
      return {
        selected: "",
        maps: [
          {
            id: "tianditu",
            type: "wmts",
            title: "天地图",
            url: "",
            pathname:
              "/img_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles",
          },
          {
            id: "arcgisonline",
            type: "wmts",
            title: "ArcGIS Online",
            url: "https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
          },
          {
            id: "blank",
            url: "http://multiverse.vothing.com/ref/style/blank.json",
            type: "underlay",
            noToken: true, //不需要token验证的mapbox底图
          },
        ],
        isdealing: false,
      };
    },
    computed: {
      mapsComputed() {
        return this.maps.filter((item) => item.id !== "blank");
      },
    },
    created() {
      this.setTiandituMapUrl();
    },
    methods: {
      onItemClick(item) {
        this.handleAddwmtsFeature(item.id);
      },
      //添加天地图 或 arcgis
      async handleAddwmtsFeature(mapId) {
        if (window.scene?.features.has("underlay") && mapId === this.selected) {
          return;
        }
        this.isdealing = true;
        this.selected = mapId;
        //如果没有底图，需要先添加一个空白球体
        if (!window.scene.features.has("underlay")) {
          await this.addBlankBall();
        }

        //天地图的底图 每次点击的时候先移除一下
        //因为wmts wms tms等，不支持直接修改url
        //如果需要替换新的url，只能先移除，再添加
        window.scene.removeFeature("underlay_wmts", true);

        setTimeout(() => {
          const item = this.maps.find((ele) => ele.id === mapId);
          //当做底图来添加的天地图或arcgis，固定设置一个特殊的id
          let wmts = window.scene.findFeature("underlay_wmts");
          if (!wmts) {
            wmts = window.scene.addFeature("wmts", "underlay_wmts");
          }
          wmts.name = item.title;
          wmts.url = item.url;
          wmts.priority = 0;
          wmts.always = false;
          if (!wmts.loaded) {
            wmts.load();
          }
          window.scene.render();
          this.isdealing = false;
        });
      },
      setTiandituMapUrl() {
        //生成随机下标，这里取8，是因为天地图是t0~t7
        const eq = Math.floor(Math.random() * 8);
        //获取当前站点的协议
        const protocol = window.location.protocol;
        let origin = "";
        if (protocol == "http:") {
          origin = `http://t${eq}.tianditu.com`;
        } else {
          origin = `https://t${eq}.tianditu.gov.cn`;
        }
        const tiandituMapItem = this.maps.find((item) => item.id === "tianditu");
        if (tiandituMapItem) {
          //拼接天地图地址，
          tiandituMapItem.url = origin + tiandituMapItem.pathname;
        }
      },
      async addBlankBall() {
        const item = this.maps.find((item) => item.id === "blank");
        if (!item) return;
        const res = await fetch(item.url)
          .then((res) => res.json())
          .catch(() => {});
        if (!res) return;
        res.sprite = "http://multiverse.vothing.com/ref/style/sprite";
        const feature = window.scene.addFeature(item.type);
        feature.url = res;
        feature.name = item.title;
        feature.load();
        window.scene.render();
      },
    },
  };
</script>
<style>
  .map.activated {
    @apply bg-[#334CAF];
  }
</style>
