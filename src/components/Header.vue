<template>
  <header class="headerWrapper">
    <div class="relative z-[100] w-full h-[72px]">
      <logo />
      <div class="absolute top-0 left-0 w-full h-full px-[16px] flex items-center justify-between">
        <drop-down :options="optionList" :selectedId="selectedId" @change="onProjectChange" class="float-left" />
        <sys-actions class="float-right" />
      </div>
    </div>
  </header>
</template>

<script>
  import DropDown from "./DropDown.vue";
  import SysActions from "./SysActions.vue";
  import Logo from "./Logo.vue";

  export default {
    name: "AppHeader",
    components: {
      "drop-down": DropDown,
      "sys-actions": SysActions,
      logo: Logo,
    },
    props: {
      selectedId: {
        type: String,
        default: "",
      },
      optionList: {
        type: Array,
        default: () => [],
      },
    },
    methods: {
      onProjectChange(data) {
        this.$store.commit("common/setCurrentProject", data);
      },
    },
  };
</script>
