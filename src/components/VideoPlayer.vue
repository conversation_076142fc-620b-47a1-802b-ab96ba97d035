<template>
  <video ref="videoPlayer" class="video-js vjs-big-play-centered" v-bind="$attrs"></video>
</template>

<script>
  import videojs from "video.js";
  import "video.js/dist/video-js.min.css";
  import { merge } from "lodash-es";

  export default {
    name: "VideoPlayer",
    inheritAttrs: false,
    props: {
      options: {
        type: Object,
        default() {
          return {};
        },
      },
    },
    data() {
      return {
        player: null,
        defaultOptions: {
          autoplay: "play", // false true muted play any
          controls: true,
          loop: false,
          muted: true,
          preload: "auto", // auto metadata none

          fluid: false,
          language: "zh-CN",
          notSupportedMessage: "此视频暂无法播放，请稍后再试",
          controlBar: {
            remainingTimeDisplay: true,
            currentTimeDisplay: true,
            timeDivider: true,
            durationDisplay: true,
            progressControl: true,
            customControlSpacer: true,
            fullscreenToggle: true,
            volumePanel: {
              inline: false,
            },
          },
          liveTracker: { trackingThreshold: 5, liveTolerance: 5 },
        },
      };
    },
    computed: {
      finalOptions() {
        return merge({}, this.defaultOptions, this.options);
      },
    },
    mounted() {
      this.init();
    },
    beforeDestroy() {
      this.player?.dispose();
      this.$emit("dispose");
    },
    methods: {
      init() {
        const that = this;
        videojs(this.$refs.videoPlayer, this.finalOptions, function () {
          that.player = this;
          that.$emit("ready", this);
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::v-deep .vjs-control-bar {
    background-color: rgba(0, 0, 0, 0.6);
  }

  ::v-deep .vjs-slider-horizontal {
    .vjs-volume-level::before {
      top: -4px;
    }
    .vjs-play-progress::before {
      top: -5px;
    }
  }

  ::v-deep .vjs-slider-vertical .vjs-volume-level:before {
    left: -4px;
  }
</style>
