<template>
  <span @click="onFullscreen" class="inline-flex justify-center items-center w-[42px] h-[42px] rounded-md cursor-pointer bg-[hsla(0,0%,0%,0.9)]">
    <img class="w-[18px] h-[18px]" src="../assets/images/fullscreen.png" alt="全屏" />
  </span>
</template>

<script>
  export default {
    name: "FullscreenButton",
    methods: {
      onFullscreen() {
        if (this.isFullscreenEnabled()) {
          if (this.isFullScreen()) {
            this.exitFullscreen();
          } else {
            this.openFullScreen(document.body);
          }
        }
      },
      // 判断当前文档是否能切换到全屏
      isFullscreenEnabled() {
        return document.fullscreenEnabled || document.mozFullScreenEnabled || document.webkitFullscreenEnabled || document.msFullscreenEnabled;
      },
      // 判断当前窗体是否为全屏状态
      isFullScreen() {
        return !!(document.fullscreenElement || document.mozFullScreen || document.webkitIsFullScreen || document.webkitFullScreen || document.msFullScreen);
      },
      // 打开全屏
      openFullScreen(ele) {
        if (ele.requestFullscreen) {
          ele.requestFullscreen();
        } else if (ele.mozRequestFullScreen) {
          ele.mozRequestFullScreen();
        } else if (ele.webkitRequestFullscreen) {
          ele.webkitRequestFullscreen();
        } else if (ele.msRequestFullscreen) {
          ele.msRequestFullscreen();
        }
        this.$store.dispatch("common/setFullscreenInfo", {
          isFullscreen: true,
          fullscreenElement: ele,
        });
      },
      // 退出全屏
      exitFullscreen() {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (element.msExitFullscreen) {
          element.msExitFullscreen();
        }
        this.$store.dispatch("common/setFullscreenInfo", {
          isFullscreen: false,
          fullscreenElement: null,
        });
      },
    },
  };
</script>
