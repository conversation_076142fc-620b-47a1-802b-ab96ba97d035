<template>
  <div class="logoWrapper relative h-full min-h-[72px]">
    <img class="absolute z-50 left-[50%] top-[50%] h-[40px] fixPosition" src="../assets/images/logo.png" alt="logo" />
    <img class="absolute -z-50 w-full h-full" src="../assets/images/header-bg.png" alt="header-bg" />
  </div>
</template>

<script>
  export default {
    name: "Logo",
  };
</script>

<style scoped>
  .fixPosition {
    transform: translate3d(calc(-50% - 1.5rem), -55%, 0);
  }
</style>
