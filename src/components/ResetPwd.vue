<template>
  <div class="resetpwd-wrapper">
    <div class="video-wrapper">
      <video class="w-full h-full object-fill" muted autoplay="true" loop="loop" poster="/login/bg-video-poster.jpg">
        <source src="/login/bg-video.mp4" type="video/mp4" />
      </video>
    </div>
    <div class="form-wrapper">
      <img class="logo" src="/login/logo.png" alt="logo" />
      <div class="form">
        <div class="content">
          <div class="title">重置密码</div>
          <el-form
            label-position="right"
            label-width="120px"
            ref="loginForm"
            :model="formInline"
            size="medium"
            :rules="formInline.rules"
            @keyup.enter.native="doReset"
          >
            <el-form-item prop="id" label="账号">
              <el-input class="el-formitem-cus id" v-model="formInline.id" placeholder="请输入您的账号"></el-input>
            </el-form-item>
            <el-form-item prop="pwd1" label="旧密码">
              <el-input class="el-formitem-cus pwd" v-model="formInline.pwd1" placeholder="请输入旧密码" :type="formInline.type1">
                <div slot="suffix" @click.stop="toggleInputType1" class="hover:cursor-pointer">
                  <img v-if="isPassword1" src="/login/eye-closed.png" alt="密码" />
                  <img v-else src="/login/eye.png" alt="文本" />
                </div>
              </el-input>
            </el-form-item>
            <el-form-item prop="pwd2" label="新密码">
              <el-input class="el-formitem-cus pwd" v-model="formInline.pwd2" placeholder="请输入新密码" :type="formInline.type2">
                <div slot="suffix" @click.stop="toggleInputType2" class="hover:cursor-pointer">
                  <img v-if="isPassword2" src="/login/eye-closed.png" alt="密码" />
                  <img v-else src="/login/eye.png" alt="文本" />
                </div>
              </el-input>
            </el-form-item>
            <el-form-item class="text-right">
              <el-button class="el-formitem-cus btn-reset" type="primary" :loading="isReseting" @click="doReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { doResetPwd } from "../api/login";
import { encrypt } from "../api/code";
export default {
  name: "ResetPwd",
  data() {
    return {
      formInline: {
        id: "",
        pwd1: "",
        pwd2: "",
        type1: "password",
        type2: "password",
        rules: {
          id: [{ required: true, message: "请输入您的账号", trigger: "change" }],
          pwd1: [{ required: true, message: "请输入旧密码", trigger: "change" }],
          pwd2: [{ required: true, message: "请输入新密码", trigger: "change" }],
        },
      },
      isReseting: false,
    };
  },
  computed: {
    isPassword1() {
      return this.formInline.type1 === "password";
    },
    isPassword2() {
      return this.formInline.type2 === "password";
    },
  },
  methods: {
    toggleInputType1() {
      if (this.formInline.type1 === "password") {
        this.formInline.type1 = "text";
      } else {
        this.formInline.type1 = "password";
      }
    },
    toggleInputType2() {
      if (this.formInline.type2 === "password") {
        this.formInline.type2 = "text";
      } else {
        this.formInline.type2 = "password";
      }
    },
    doReset() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.isReseting = true;
          doResetPwd({ name: this.formInline.id, oldPwd: encrypt(this.formInline.pwd1), pwd: encrypt(this.formInline.pwd2) })
            .then((resetResult) => {
              this.isReseting = false;
              if (resetResult?.code === 0) {
                this.$message.success(resetResult.message ?? "密码重置成功,请重新登录");
                this.$router.replace("/");
              }
            })
            .catch(() => {
              this.isReseting = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.resetpwd-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  .video-wrapper {
    position: absolute;
    z-index: -1;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  .form-wrapper {
    margin-top: 180px;
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    .logo {
      margin: 0 auto;
      user-select: none;
    }
    .form {
      position: relative;
      z-index: 2;
      display: flex;
      justify-content: center;
      .content {
        display: flex;
        position: absolute;
        z-index: 90;
        margin-top: 60px;
        @apply font-pingfang;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        ::v-deep .el-form-item__label {
          line-height: 50px;
          font-size: 20px;
          color: #999;
          user-select: none;
        }
        ::v-deep .el-form-item {
          margin-bottom: 40px;
        }
        ::v-deep .el-formitem-cus {
          height: 50px;
          border: none;
          font-size: 16px;
          &.id,
          &.pwd {
            width: 360px;
            .el-input__inner {
              height: 50px;
              background-color: #5b6693;
              border-radius: 6px;
            }
          }

          .el-input__suffix {
            &-inner {
              display: flex;
              height: 100%;
              align-items: center;
            }
          }
          &.btn-reset {
            width: 80px;
            background-color: #2749ce;
          }
        }
        .title {
          margin-left: 80px;
          padding-bottom: 32px;
          @apply font-pingfang;
          color: #fff;
          font-size: 22px;
          user-select: none;
        }
      }
    }
  }
}
</style>
