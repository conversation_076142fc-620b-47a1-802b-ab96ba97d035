<template>
  <div class="menuListWrapper inline-block rounded-tr-[34px] rounded-br-[34px] w-[13.5rem] h-[45rem] pt-2">
    <router-link
      v-for="item in localMenusFiltered"
      :key="item.id"
      :to="item.to"
      class="relative z-[100] menuItem w-48 h-16 flex items-center pl-6 py-4 pr-5 my-3 rounded-tr-[12px] rounded-br-[12px] cursor-pointer"
      active-class="activated"
      exact
    >
      <img class="w-8" :src="item.icon" alt="菜单图标" />
      <div class="ml-4 text-[18px] text-[#FFF] text-opacity-60 menuName">{{ item.label }}</div>
    </router-link>
    <div
      v-for="item in thirdMenus"
      :key="item.id"
      class="relative z-[100] thirdMenuItem w-48 h-16 flex items-center pl-6 py-4 pr-5 my-3 rounded-tr-[12px] rounded-br-[12px]"
      :class="{
        hovered: activatedThirdMenu2?.id === item.id,
        activated: activatedThirdMenu?.id === item.id,
      }"
      @mouseenter="onThirdMenuEnter(item)"
      @mouseleave="onThirdMenuLeave(item)"
    >
      <img class="w-8" :src="item.icon" alt="菜单图标" />
      <div class="ml-4 font-pingfang text-[18px] text-[#FFF] text-opacity-60 menuName">{{ item.label }}</div>
      <div v-show="item.subMenus?.length && activatedThirdMenu2?.id === item.id" class="absolute top-6 left-full min-w-[260px] submenusWrapper">
        <div
          v-for="submenu in item.subMenus"
          :key="submenu.id"
          @click.stop="onThirdMenuClick(submenu, item)"
          class="submenuItem"
          :class="{ activated: activatedThirdSubmenu?.id === submenu.id }"
        >
          <a v-if="submenu.needInvokeLocalApp" :href="submenu.to" target="_blank">{{ submenu.label }}</a>
          <span v-else>{{ submenu.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import CryptoJS from "crypto-js";
  import { Message } from "element-ui";
  import { mapGetters } from "vuex";

  let workflowLoginURL;
  let workflowWebURL;
  let pid;
  if (import.meta.env.PROD) {
    //生产环境
    const ProjectConfig = window.ProjectConfig;
    workflowLoginURL = ProjectConfig.workflowLoginURL;
    workflowWebURL = ProjectConfig.workflowWebURL;
    pid = ProjectConfig.pid;
  } else {
    //非生产环境
    workflowLoginURL = "http://workflow-newapi.probim.cn/api/User/Home/Login";
    workflowWebURL = "http://workflow.probim.cn";
    pid = "71f7a5df-a7e3-42f4-bcea-36d7d2924ddd";
  }

  export default {
    name: "MenuList",
    data() {
      return {
        activatedThirdMenu2: null,
        PublicKey: "MK4ZJF10PRO19*#8",
      };
    },
    computed: {
      ...mapGetters("common", [
        "localMenus",
        "thirdMenus",
        "activatedThirdSubmenu",
        "activatedThirdMenu",
        "currentProjectId",
        "projectIdsWithoutMoldBase",
        "projectIdPNMap",
      ]),
      localMenusFiltered() {
        if (this.projectIdsWithoutMoldBase.includes(this.currentProjectId)) {
          return this.localMenus.filter((item) => item.id !== "mold-base");
        } else {
          return this.localMenus;
        }
      },
    },
    methods: {
      onThirdMenuClick(item, parentItem) {
        if (item?.isLocal === true) {
          if (this.$route.path === item.to) return;
          item?.to &&
            this.$router?.push(item.to).then(() => {
              this.$store.commit("common/setActivatedThirdMenu", parentItem);
              this.$store.commit("common/setActivatedThirdSubmenu", item);
            });
        } else {
          if (item?.needInvokeLocalApp) {
            return;
          } else if (item?.needLocalHandle) {
            const id = item.id;
            if (id === "construction-1") {
              //绿色建造九宫格管理系统
              this.doVmsEnvmonitorLoginRedirect();
            } else if (id === "construction-2") {
              //协同
              this.doSelfLoginRedirect(item);
            } else if (id === "construction-3") {
              const pn = this.projectIdPNMap[this.currentProjectId];
              if (!/^\s*$/i.test(pn + "")) {
                window.open(`http://xmglappjs.sh2j.com/web/public/index.php/fanganapi/index/index?pn=${pn}`);
              }
            }
          } else {
            item?.to && window.open(item.to);
          }
        }
      },
      onThirdMenuEnter(item) {
        this.activatedThirdMenu2 = item;
      },
      // eslint-disable-next-line no-unused-vars
      onThirdMenuLeave(item) {
        this.activatedThirdMenu2 = null;
      },

      //登录绿色建造九宫格管理系统
      async doVmsEnvmonitorLoginRedirect() {
        window.open("http://127.0.0.1:9000/vms-envmonitor-webapp/");
      },

      //登录自己的协同平台
      async doSelfLoginRedirect(item) {
        const userInfo = {
          UserName: "<EMAIL>",
          Password: this.computepwdstr("htj123456" + this.PublicKey, CryptoJS),
        };
        // const userInfo = {
        //   UserName: "admin",
        //   Password: this.computepwdstr("admin" + this.PublicKey, CryptoJS),
        // };
        const postData = Object.keys(userInfo)
          .map((key) => {
            return `${key}=${encodeURIComponent(userInfo[key])}`;
          })
          .join("&");
        const res = await this.$http({
          method: "post",
          url: workflowLoginURL,
          data: postData,
          withCredentials: false,
          withProjectId: false,
          withToken: false,
          baseURL: "/",
        });
        if (res?.code === 0) {
          const token = res.data?.token;
          if (token) {
            window.open(`${workflowWebURL}/#/Home/Boot/${token}/0/${pid}`);
          } else {
            Message.error({ message: `${item.label}登录失败,请稍后再试`, duration: 1000 * 3 });
          }
        } else {
          Message.error({ message: `${item.label}登录失败,请稍后再试`, duration: 1000 * 3 });
        }
      },
      computepwdstr(pwdorigin, _this_CryptoJS) {
        const afterpwd = this.SymmetricEncryption(
          {
            input: pwdorigin,
            key: this.PublicKey,
          },
          _this_CryptoJS,
        );
        return afterpwd;
      },
      SymmetricEncryption(json, CryptoJS) {
        if (!json || !json.input || !json.key) {
          console.warn("json, json.input and json.key are not allow null!");
          return -1;
        }
        if (json.key.length === 0) {
          console.warn("json.length should not be zero!");
          return -1;
        }
        while (json.key.length < 16) {
          json.key = json.key + json.key;
        }
        const KEY = json.key;
        const IV = "*BIM19FF4KMY0R8*";
        const key = CryptoJS.enc.Utf8.parse(KEY);
        const iv = CryptoJS.enc.Utf8.parse(IV);
        const srcs = CryptoJS.enc.Utf8.parse(json.input);
        const encrypted = CryptoJS.AES.encrypt(srcs, key, {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7,
        });
        return encrypted.ciphertext.toString();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .menuListWrapper {
    background: linear-gradient(0deg, hsla(235, 24%, 9%, 0.9) 0%, hsla(236, 33%, 18%, 0.9) 100%) no-repeat left center / cover border-box;
    box-shadow: -1px 0px 25px 0px rgba(14, 15, 15, 0.1);
  }
  .menuItem:not(.activated):hover {
    background: url("/images/menu-hover.png") no-repeat left center / cover border-box;
  }
  .menuItem.activated {
    background: linear-gradient(to right, #1c635a, #4edeca) no-repeat left center/cover border-box;
    .menuName {
      @apply text-opacity-100;
    }
  }

  .thirdMenuItem:not(.activated).hovered {
    background: url("/images/menu-hover.png") no-repeat left center / cover border-box;
  }
  .thirdMenuItem.activated {
    background: linear-gradient(to right, #1c635a, #4edeca) no-repeat left center/cover border-box;
    .menuName {
      @apply text-opacity-100;
    }
  }
  .submenusWrapper {
    .submenuItem {
      @apply h-[60px] px-6 flex justify-start items-center font-pingfang text-base bg-[hsla(233,13%,13%,1)] cursor-pointer;
      &:first-of-type {
        @apply rounded-tl-[12px] rounded-tr-[12px];
        &:only-child {
          @apply rounded-bl-[12px] rounded-br-[12px];
        }
      }
      &:last-of-type:not(:first-of-type) {
        @apply rounded-bl-[12px] rounded-br-[12px];
      }
      &:not(.activated):hover {
        @apply bg-[hsla(228,67%,48%,1)];
      }
      &.activated {
        @apply bg-[#485eb7];
      }
    }
  }
</style>
