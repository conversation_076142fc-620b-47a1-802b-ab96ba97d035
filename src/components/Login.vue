<template>
  <div class="login-wrapper">
    <div class="video-wrapper">
      <video class="w-full h-full object-fill" muted autoplay="true" loop="loop" poster="/login/bg-video-poster.jpg">
        <source src="/login/bg-video.mp4" type="video/mp4" />
      </video>
    </div>
    <div class="form-wrapper">
      <img class="logo" src="/login/logo.png" alt="logo" />
      <div class="form">
        <div class="content">
          <el-form ref="loginForm" :inline="true" :model="formInline" size="medium" :rules="formInline.rules" @keyup.enter.native="doLogin">
            <el-form-item prop="id">
              <el-input class="el-formitem-cus id" v-model="formInline.id" placeholder="请输入您的账号"></el-input>
            </el-form-item>
            <el-form-item prop="pwd">
              <el-input class="el-formitem-cus pwd" v-model="formInline.pwd" placeholder="请输入您的密码" :type="formInline.type">
                <div slot="suffix" @click.stop="toggleInputType" class="hover:cursor-pointer">
                  <img v-if="isPassword" src="/login/eye-closed.png" alt="密码" />
                  <img v-else src="/login/eye.png" alt="文本" />
                </div>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button class="el-formitem-cus btn-login" type="primary" :loading="isAuthenticating" @click="doLogin">登录</el-button>
            </el-form-item>
            <el-form-item class="rember">
              <el-checkbox v-model="formInline.rember">记住我</el-checkbox>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { doAuthenticateLogin } from "../api/authenticate";
export default {
  name: "Login",
  data() {
    return {
      formInline: {
        id: "",
        pwd: "",
        type: "password",
        rember: false,
        rules: {
          id: [{ required: true, message: "请输入账号", trigger: "change" }],
          pwd: [{ required: true, message: "请输入密码", trigger: "change" }],
        },
      },
    };
  },
  computed: {
    ...mapGetters("common", ["isAuthenticating", "token"]),
    isPassword() {
      return this.formInline.type === "password";
    },
  },
  methods: {
    toggleInputType() {
      if (this.formInline.type === "password") {
        this.formInline.type = "text";
      } else {
        this.formInline.type = "password";
      }
    },
    doLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          doAuthenticateLogin({ name: this.formInline.id, pwd: this.formInline.pwd }).then((loginResult) => {
            if (loginResult?.code === 0) {
              this.$router.replace("/home");
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.login-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  .video-wrapper {
    position: absolute;
    z-index: -1;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  .form-wrapper {
    margin-top: 180px;
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    .logo {
      margin: 0 auto;
      // width: 606px;
      // height: auto;
    }
    .form {
      position: relative;
      z-index: 2;
      margin-top: 70px;
      width: 966px;
      min-height: 186px;
      background: url("/login/form-bg.png") center/cover no-repeat;
      .content {
        display: flex;
        position: absolute;
        z-index: 90;
        top: 70px;
        left: 40px;
        @apply space-x-3;
        @apply font-pingfang;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        ::v-deep .el-formitem-cus {
          height: 50px;
          border: none;
          font-size: 16px;
          &.id,
          &.pwd {
            width: 360px;
            .el-input__inner {
              height: 50px;
              background-color: #5b6693;
              border-radius: 6px;
            }
          }
          .el-input__suffix {
            &-inner {
              display: flex;
              height: 100%;
              align-items: center;
            }
          }
          &.btn-login {
            width: 130px;
            background-color: #2749ce;
          }
        }
        ::v-deep .rember {
          margin: 0;
          margin-top: -8px;
          width: 100%;
          .el-checkbox__label {
            color: #d8d7da;
          }
        }
      }
    }
  }
}
</style>
