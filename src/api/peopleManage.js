import { http } from "../utils/http";

export const PeopleManageAPI = {
  getTodayTotal: (data, config = {}) => {
    return http.post("/beh/person/todayTotal", data, config);
  },

  getWorkerCategoryData: (data, config = {}) => {
    return http.post("/beh/person/workTypeTotal", data, config);
  },

  getHealthRiskData: (data, config = {}) => {
    return http.post("/beh/person/risk/health", data, config);
  },

  getLocationRiskData: (data, config = {}) => {
    return http.post("/beh/person/risk/location", data, config);
  },

  getBehaviorRiskData: (data, config = {}) => {
    return http.post("/beh/person/risk/behavior", data, config);
  },

  getPeopleList: (data, config = {}) => {
    return http.post("/beh/person/presents", data, config);
  },

  getPersonInfo: (data, config = {}) => {
    return http.post("/beh/person/info", data, config);
  },

  getPersonScoreData: (data, config = {}) => {
    return http.post("/beh/person/score", data, config);
  },

  getPersonAlarmList: ({ id, ...others }, config = {}) => {
    return http.post(`/beh/person/risks/${id}`, others, config);
  },

  getPersonPunishmentList: (data, config = {}) => {
    return http.post("/beh/person/punishment", data, config);
  },

  getTodayPersonExitEntranceData: () => {
    return http.get("/beh/person/GetTodayPersonExitEntranceData");
  },

  getPersonAlarms: (data, config = {}) => {
    return http.post("/beh/person/alarms", data, config);
  },

  getRiskData: (data, config = {}) => {
    return http.post("/beh/environment/GetRiskData", data, config);
  },
  getSensorTypeTree(data, config = {}) {
    return http.post("/beh/person/tree", data, config);
  },
};
