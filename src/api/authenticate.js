import { doLogin } from "./login";
import { encrypt } from "./code"; //getCode
import store from "../store/index";
export const doAuthenticateLogin = async (loginInfo) => {
  store.commit("common/setIsAuthenticating", true);
  // const userInfo = {
  //   name: "admin",
  //   pwd: "csmp@2021",
  //   r: new Date().getTime(),
  // };
  const userInfo = {
    name: loginInfo.name,
    pwd: loginInfo.pwd,
    r: new Date().getTime()
  }
  const code = encrypt(JSON.stringify(userInfo));
  // store.commit("common/setCode", code);
  const loginResult = await doLogin({ code }, { withProjectId: false, withToken: false });
  store.commit("common/setIsAuthenticating", false);

  if (loginResult?.code === 0) {
    store.commit("common/setCode", code);
    const { token, ...userProfile } = loginResult.data;
    store.commit("common/setToken", token);
    store.commit("common/setUserProfile", userProfile);
    window.localStorage.setItem("token", token);
    window.localStorage.setItem("userProfile", JSON.stringify(userProfile ?? null));
    if (token) {
      return { code: 0, token };
    } else {
      return { code: 1, token, message: loginResult?.message ?? "登录失败" };
    }
  }
};
