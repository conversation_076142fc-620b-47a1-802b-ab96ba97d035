import { http } from "../utils/http";
export const HomeAPI = {
  getProjectList: (config = {}) => {
    return http.get("/beh/home/<USER>", config);
  },

  getProjectInfo: (data, config = {}) => {
    return http.post("/beh/summary/getPrjSummary", data, config);
  },

  getWorkerCategoryData: (data, config = {}) => {
    return http.post("/beh/person/workTypeTotal", data, config);
  },

  getEquipmentCategoryCount: (data, config = {}) => {
    return http.post("/beh/equipment/total", data, config);
  },

  getVideoAreaList: (belong, data, config = {}) => {
    return http.post(`/beh/video/area?belong=${belong}`, data, config);
  },

  getEnvPointInfo: () => {
    return http.get("/beh/environment/getPointEnvironmentRealData");
  },

  getVideoByAreaId: (data, config = {}) => {
    return http.post("/beh/video", data, config);
  },

  getTotalGoingData: () => {
    return http.get("/beh/home/<USER>");
  },

  getProjectTypeSummaryData: () => {
    return http.get("/beh/home/<USER>");
  },

  getTradeCategoriesStatisticsData: () => {
    return http.get("/beh/home/<USER>");
  },
  getVideoSourceList() {
    return http.post("/beh/person/tree");
  }
};
