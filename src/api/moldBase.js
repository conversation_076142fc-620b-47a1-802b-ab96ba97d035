import { http } from "../utils/http";

export const MoldBaseAPI = {
  getSensorTypeList: ({ belong, ...others }, config = {}) => {
    return http.post(`/beh/survey/categories?belong=${belong}`, others, config);
  },
  getVideoList: (data, config = {}) => {
    return http.post("/beh/video", data, config);
  },
  getSensorData: ({ categoryId, ...others }, config = {}) => {
    return http.post("/beh/survey/points?categoryId=" + categoryId, others, config);
  },
  getSteelPlateData: (data, config = {}) => {
    return http.post("/beh/survey/points?categoryId=11", data, config);
  },

  getMoldBaseInfo: (data, config = {}) => http.post("/beh/survey/getMoldBaseInfo", data, config),

  getMonitorDataByPoints: (data, config = {}) => http.post("/beh/survey/getMonitorDataByPoints", data, config),

  getHorizontalUp: () => http.get("/beh/survey/GetMoldBaseHorizon"),

  getVerticalPosition: () => http.get("/beh/survey/getVerticalLocationData"),

  getPointsListLine: (data) => http.post(`/beh/point/getPointCurveData?typeId=${data.typeId}`),

  getPointInfoList: (data) => {
    return http.get("/beh/survey/getPointDetailList", { params: data });
  },
  getVideoSourceList() {
    return http.post("/beh/person/tree");
  }
};
