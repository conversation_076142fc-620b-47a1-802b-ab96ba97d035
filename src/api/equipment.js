import { http } from "../utils/http";

export const EquipmentAPI = {
  getEquipmentAlarmList: (data, config = {}) => {
    return http.post("/beh/equipment/risks", data, config);
  },

  getEquipmentAlarmTotal: (data, config = {}) => {
    return http.post("/beh/equipment/alarm/total", data, config);
  },

  getHereEquipmentList: (data, config = {}) => {
    return http.post("/beh/equipment/total", data, config);
  },

  getEquipmentList: ({ id, ...others }, config = {}) => {
    return http.post(`/beh/equipment/list/${id}`, others, config);
  },

  getSpecifyEquipmentDetail: ({ id, ...others }, config = {}) => {
    return http.post(`/beh/equipment/detail/${id}`, others, config);
  },

  getSpecifyEquipmentRisk: ({ id, ...others }, config = {}) => {
    return http.post(`/beh/equipment/risk/${id}`, others, config);
  },

  getSpecifyEquipmentAlarm: ({ id, ...others }, config = {}) => {
    return http.post(`/beh/equipment/alarm/${id}`, others, config);
  },

  getSpecifyEquipmentLift: ({ id, ...others }, config = {}) => {
    // return http.post(`/beh/equipment/lift/${id}`, others, config);
    return http.post(`/beh/equipment/newlift/${id}`, others, config);
  },
  getSpecifyEquipmentLift2: ({ id, ...others }, config = {}) => {
    return http.post(`/beh/equipment/lift/${id}`, others, config);
  },
  getSpecifyEquipment: ({ id, ...others }, config = {}) => {
    return http.post(`/beh/equipment/data/${id}`, others, config);
  },
  getEnclosureAlarmList: ({ wdGroupId, ...others }, config = {}) => {
    return http.post(`/beh/wd/LostList/${wdGroupId}`, others, config);
  },
  getTowerCraneLastestData() {
    return http.post(`/beh/equipment/TowerCraneLastestData`)
  },
  getTaDiaoHaveRisk() {
    return http.post(`/beh/equipment/GetTaDiaoHaveRisk`)
  },
};
