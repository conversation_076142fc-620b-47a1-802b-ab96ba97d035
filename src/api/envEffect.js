import { http } from "../utils/http";
export const EnvEffectAPI = {
  //根据选中的测点获取信息
  getPointEnvironmentRealData(data) {
    return http.get("/beh/environment/getPointEnvironmentRealData", { params: data });
  },

  //环境监测页签
  getProjectGroupPoints() {
    return http.get("/beh/environment/GetProjectGroupPoints");
  },

  //视频监控页签
  getVideoList() {
    return http.get("/beh/environment/getVideoList");
  },

  //获取24小时环境监测
  getPointDayHourEnvironmentRealData(data) {
    return http.get("/beh/environment/getPointDayHourEnvironmentRealData", { params: data });
  },

  //测点预警数据
  getMeasurePointWarningList(data) {
    return http.get("/beh/environment/getPointEnvironmentNewData", { params: data });
  },
};
