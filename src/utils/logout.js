import store from "../store/index";
import router from "../router";

const clearAuthenticateInfo = () => {
    store.commit("common/setToken", "");
    store.commit("common/setUserProfile", null);
    window.localStorage.removeItem("token");
    window.localStorage.removeItem("userProfile");
}

export const toLoginPage = () => {
    clearAuthenticateInfo();
    router.replace("/login");
}

export const toResetPwdPage = () => {
    clearAuthenticateInfo();
    router.replace("/resetpwd");
}