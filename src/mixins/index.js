import { mapGetters } from "vuex";
export const mixinGetters = {
  computed: {
    ...mapGetters("common", [
      "token",
      "projectList",
      "currentProject",
      "currentProjectId",
      "isFullscreen",
      "workerCategoryColorMap",
      "equipmentsColorMap",
      "projectTypeColorMap",
      "inoutColorMap",
      "activatedThirdMenu",
      "activatedThirdSubmenu",
      "localMenus",
      "thirdMenus",
      "concretePumpPerformanceColorMap",
      "isSceneLoadComplete",
      "sensorTypeIdPrefixMap",
    ]),
  },
  methods: {
    getImageUrl(path) {
      return new URL(path, import.meta.url).href;
    },
  },
};
