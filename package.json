{"name": "shanghaiproject", "version": "1.0.0", "description": "Shanghai construction saftey monitor project", "author": "shanghaiproject", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint-fix": "eslint --max-warnings 0  \"src/**/*.{vue,js}\" --fix", "lint": "eslint --max-warnings 0  \"src/**/*.{vue,js}\""}, "devDependencies": {"@vitejs/plugin-vue2": "^2.2.0", "autoprefixer": "^10.4.13", "eslint": "^8.30.0", "eslint-config-prettier": "^8.5.0", "eslint-define-config": "^1.12.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.8.0", "postcss-pxtorem": "^6.0.0", "prettier": "^2.8.1", "sass": "^1.57.1", "tailwindcss": "^3.2.7", "vite": "^4.0.3"}, "dependencies": {"axios": "^1.2.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "echarts": "^5.4.1", "element-ui": "^2.15.12", "lodash-es": "^4.17.21", "nanoid": "^4.0.0", "nprogress": "^0.2.0", "video.js": "^7.20.3", "vue": "^2.7.14", "vue-router": "^3.6.5", "vue-seamless-scroll": "^1.1.23", "vue-template-compiler": "^2.7.14", "vuex": "^3.6.2"}}