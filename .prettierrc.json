{"printWidth": 160, "tabWidth": 2, "useTabs": false, "semi": true, "vueIndentScriptAndStyle": true, "singleQuote": false, "quoteProps": "as-needed", "bracketSpacing": true, "trailingComma": "all", "jsxBracketSameLine": false, "jsxSingleQuote": false, "arrowParens": "always", "insertPragma": false, "requirePragma": false, "proseWrap": "never", "htmlWhitespaceSensitivity": "strict", "endOfLine": "auto"}